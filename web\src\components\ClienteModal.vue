<template>
  <div v-if="isOpen" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <!-- Header -->
        <div class="flex items-center justify-between pb-4 border-b">
          <h3 class="text-lg font-medium text-gray-900">
            {{ isEditing ? 'Editar Cliente' : 'Novo Cliente' }}
          </h3>
          <button @click="fechar" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Form -->
        <form @submit.prevent="salvar" class="mt-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Dados Básicos -->
            <div class="space-y-4">
              <h4 class="text-md font-medium text-gray-900 border-b pb-2">Dados Básicos</h4>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Nome/Razão Social *</label>
                <input
                  v-model="form.nome_razao_social"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Tipo de Pessoa *</label>
                <select
                  v-model="form.tipo_pessoa"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="">Selecione...</option>
                  <option value="PF">Pessoa Física</option>
                  <option value="PJ">Pessoa Jurídica</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.tipo_pessoa === 'PF' ? 'CPF' : 'CNPJ' }} *
                </label>
                <input
                  v-model="form.cpf_cnpj"
                  type="text"
                  required
                  :placeholder="form.tipo_pessoa === 'PF' ? '000.000.000-00' : '00.000.000/0000-00'"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Telefone</label>
                  <input
                    v-model="form.telefone"
                    type="text"
                    placeholder="(11) 3000-0000"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Celular</label>
                  <input
                    v-model="form.celular"
                    type="text"
                    placeholder="(11) 90000-0000"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                <input
                  v-model="form.email"
                  type="email"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div class="flex items-center">
                <input
                  v-model="form.is_active"
                  type="checkbox"
                  id="ativo"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label for="ativo" class="ml-2 block text-sm text-gray-900">Cliente Ativo</label>
              </div>
            </div>

            <!-- Endereço de Cobrança -->
            <div class="space-y-4">
              <h4 class="text-md font-medium text-gray-900 border-b pb-2">Endereço de Cobrança</h4>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">CEP *</label>
                <input
                  v-model="form.endereco_cobranca_cep"
                  type="text"
                  required
                  placeholder="00000-000"
                  @blur="buscarCep"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Logradouro *</label>
                <input
                  v-model="form.endereco_cobranca_logradouro"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Número *</label>
                  <input
                    v-model="form.endereco_cobranca_numero"
                    type="text"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Complemento</label>
                  <input
                    v-model="form.endereco_cobranca_complemento"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Bairro *</label>
                <input
                  v-model="form.endereco_cobranca_bairro"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Cidade *</label>
                  <input
                    v-model="form.endereco_cobranca_cidade"
                    type="text"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">UF *</label>
                  <select
                    v-model="form.endereco_cobranca_uf"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  >
                    <option value="">UF</option>
                    <option value="SP">SP</option>
                    <option value="RJ">RJ</option>
                    <option value="MG">MG</option>
                    <option value="RS">RS</option>
                    <option value="PR">PR</option>
                    <option value="SC">SC</option>
                    <!-- Adicionar outros estados conforme necessário -->
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- Observações -->
          <div class="mt-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Observações</label>
            <textarea
              v-model="form.observacoes"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            ></textarea>
          </div>

          <!-- Erro -->
          <div v-if="erro" class="mt-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
            {{ erro }}
          </div>

          <!-- Botões -->
          <div class="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              @click="fechar"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancelar
            </button>
            <button
              type="submit"
              :disabled="salvando"
              class="px-4 py-2 bg-indigo-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-indigo-700 disabled:opacity-50"
            >
              {{ salvando ? 'Salvando...' : 'Salvar' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { clientesService } from '../services/clientes.js'

export default {
  name: 'ClienteModal',
  props: {
    isOpen: {
      type: Boolean,
      default: false
    },
    cliente: {
      type: Object,
      default: null
    }
  },
  emits: ['close', 'saved'],
  setup(props, { emit }) {
    const salvando = ref(false)
    const erro = ref('')

    const form = ref({
      nome_razao_social: '',
      tipo_pessoa: '',
      cpf_cnpj: '',
      telefone: '',
      celular: '',
      email: '',
      endereco_cobranca_cep: '',
      endereco_cobranca_logradouro: '',
      endereco_cobranca_numero: '',
      endereco_cobranca_complemento: '',
      endereco_cobranca_bairro: '',
      endereco_cobranca_cidade: '',
      endereco_cobranca_uf: '',
      observacoes: '',
      is_active: true
    })

    const isEditing = computed(() => props.cliente && props.cliente.id)

    // Preencher form quando cliente for passado
    watch(() => props.cliente, (novoCliente) => {
      if (novoCliente) {
        Object.keys(form.value).forEach(key => {
          form.value[key] = novoCliente[key] || form.value[key]
        })
      } else {
        // Reset form
        Object.keys(form.value).forEach(key => {
          if (key === 'is_active') {
            form.value[key] = true
          } else {
            form.value[key] = ''
          }
        })
      }
    }, { immediate: true })

    const buscarCep = async () => {
      const cep = form.value.endereco_cobranca_cep.replace(/\D/g, '')
      if (cep.length === 8) {
        try {
          const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`)
          const data = await response.json()
          if (!data.erro) {
            form.value.endereco_cobranca_logradouro = data.logradouro
            form.value.endereco_cobranca_bairro = data.bairro
            form.value.endereco_cobranca_cidade = data.localidade
            form.value.endereco_cobranca_uf = data.uf
          }
        } catch (error) {
          console.error('Erro ao buscar CEP:', error)
        }
      }
    }

    const salvar = async () => {
      try {
        salvando.value = true
        erro.value = ''

        let resultado
        if (isEditing.value) {
          resultado = await clientesService.atualizar(props.cliente.id, form.value)
        } else {
          resultado = await clientesService.criar(form.value)
        }

        if (resultado.success) {
          emit('saved', resultado.data)
          fechar()
        } else {
          erro.value = resultado.message || 'Erro ao salvar cliente'
        }
      } catch (error) {
        console.error('Erro ao salvar cliente:', error)
        erro.value = error.response?.data?.message || 'Erro ao salvar cliente'
      } finally {
        salvando.value = false
      }
    }

    const fechar = () => {
      emit('close')
    }

    return {
      form,
      salvando,
      erro,
      isEditing,
      buscarCep,
      salvar,
      fechar
    }
  }
}
</script>
