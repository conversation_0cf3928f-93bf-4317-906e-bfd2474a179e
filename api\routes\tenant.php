<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CategoriaController;
use App\Http\Controllers\Api\ClienteController;
use App\Http\Controllers\Api\EquipamentoController;
use App\Http\Controllers\Api\ContratoController;
use App\Http\Controllers\Api\FinanceiroController;
use App\Http\Controllers\Api\PdfController;
use App\Http\Controllers\Api\PeriodoLocacaoController;
use App\Http\Controllers\Api\RelatorioController;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {
    Route::get('/', function () {
        return 'This is your multi-tenant application. The id of the current tenant is ' . tenant('id');
    });
});

// Rotas da API para tenants
Route::middleware([
    'api',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->prefix('api')->group(function () {

    // Rota de teste para tenant
    Route::get('/test', function () {
        return response()->json([
            'message' => 'API Tenant funcionando!',
            'tenant_id' => tenant('id'),
            'tenant_name' => tenant('name'),
            'timestamp' => now(),
        ]);
    });

    // Rotas de autenticação
    Route::group(['prefix' => 'auth'], function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('register', [AuthController::class, 'register']);
        Route::post('logout', [AuthController::class, 'logout'])->middleware('auth:api');
        Route::post('refresh', [AuthController::class, 'refresh'])->middleware('auth:api');
        Route::get('me', [AuthController::class, 'me'])->middleware('auth:api');
    });

    // Rotas protegidas por autenticação
    Route::middleware(['auth:api'])->group(function () {

        // Rota de teste protegida
        Route::get('/protected-test', function () {
            return response()->json([
                'message' => 'Rota protegida do tenant funcionando!',
                'user' => auth('api')->user(),
                'tenant_id' => tenant('id'),
                'tenant_name' => tenant('name'),
                'timestamp' => now()
            ]);
        });

        // Rotas de Categorias
        Route::apiResource('categorias', CategoriaController::class);

        // Rotas de Clientes
        Route::apiResource('clientes', ClienteController::class);

        // Rotas de Equipamentos
        Route::apiResource('equipamentos', EquipamentoController::class);

        // Rotas de Períodos de Locação
        Route::apiResource('periodos-locacao', PeriodoLocacaoController::class);
        Route::post('periodos-locacao/{id}/ativar', [PeriodoLocacaoController::class, 'ativar']);
        Route::post('periodos-locacao/{id}/desativar', [PeriodoLocacaoController::class, 'desativar']);
        Route::post('periodos-locacao/reordenar', [PeriodoLocacaoController::class, 'reordenar']);
        Route::get('periodos-locacao/obter-para-dias', [PeriodoLocacaoController::class, 'obterPeriodoParaDias']);

        // Rotas de Contratos
        Route::apiResource('contratos', ContratoController::class);
        Route::get('contratos-estatisticas', [ContratoController::class, 'estatisticas']);
        Route::post('contratos/{id}/finalizar', [ContratoController::class, 'finalizar']);
        Route::post('contratos/{id}/iniciar-retirada', [ContratoController::class, 'iniciarRetirada']);
        Route::post('contratos/{id}/cancelar', [ContratoController::class, 'cancelar']);
        Route::post('contratos/{id}/renovar', [ContratoController::class, 'renovar']);
        Route::post('contratos/{id}/gerar-faturas', [ContratoController::class, 'gerarFaturas']);

        // Rotas Financeiras
        Route::prefix('financeiro')->group(function () {
            // Contas a Receber
            Route::get('contas-receber', [FinanceiroController::class, 'contasReceber']);
            Route::post('contas-receber', [FinanceiroController::class, 'criarContaReceber']);
            Route::post('contas-receber/{id}/pagar', [FinanceiroController::class, 'pagarContaReceber']);

            // Contas a Pagar
            Route::get('contas-pagar', [FinanceiroController::class, 'contasPagar']);
            Route::post('contas-pagar', [FinanceiroController::class, 'criarContaPagar']);
            Route::post('contas-pagar/{id}/pagar', [FinanceiroController::class, 'pagarContaPagar']);

            // Recibos
            Route::get('recibos', [FinanceiroController::class, 'recibos']);
            Route::post('recibos', [FinanceiroController::class, 'criarRecibo']);

            // Dashboard e utilitários
            Route::get('dashboard', [FinanceiroController::class, 'dashboard']);
            Route::get('categorias', [FinanceiroController::class, 'categorias']);

            // Relatórios Financeiros
            Route::get('fluxo-caixa', [FinanceiroController::class, 'fluxoCaixa']);
            Route::get('inadimplencia', [FinanceiroController::class, 'inadimplencia']);
            Route::post('conciliacao-bancaria', [FinanceiroController::class, 'conciliacaoBancaria']);
        });

        // Rotas de PDFs
        Route::prefix('pdf')->group(function () {
            Route::get('contrato/{id}', [PdfController::class, 'contrato']);
            Route::get('recibo/{id}', [PdfController::class, 'recibo']);
            Route::post('solicitacao', [PdfController::class, 'solicitacao']);
            Route::get('relatorio/contas-receber', [PdfController::class, 'relatorioContasReceber']);
            Route::get('relatorio/equipamentos', [PdfController::class, 'relatorioEquipamentos']);
        });

        // Rotas de Relatórios Avançados
        Route::prefix('relatorios')->group(function () {
            Route::get('dashboard-executivo', [RelatorioController::class, 'dashboardExecutivo']);
            Route::get('performance-equipamentos', [RelatorioController::class, 'performanceEquipamentos']);
            Route::get('analise-financeira', [RelatorioController::class, 'analiseFinanceira']);
            Route::get('clientes', [RelatorioController::class, 'relatorioClientes']);
        });

        // Rota de Dashboard
        Route::get('/dashboard', function () {
            return response()->json([
                'message' => 'Dashboard data',
                'user' => auth('api')->user(),
                'tenant_id' => tenant('id'),
                'tenant_name' => tenant('name'),
                'stats' => [
                    'clientes_total' => \App\Models\Cliente::count(),
                    'equipamentos_total' => \App\Models\Equipamento::count(),
                    'contratos_ativos' => \App\Models\Contrato::where('status', 'ativo')->count(),
                    'receita_mensal' => \App\Models\Recibo::entradas()
                        ->whereMonth('data_emissao', now()->month)
                        ->whereYear('data_emissao', now()->year)
                        ->sum('valor'),
                    'contas_receber_pendentes' => \App\Models\ContaReceber::pendentes()->count(),
                    'contas_pagar_pendentes' => \App\Models\ContaPagar::pendentes()->count()
                ]
            ]);
        });

    });
});
