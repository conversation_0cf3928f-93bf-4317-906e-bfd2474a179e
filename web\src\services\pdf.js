import api from './api.js'

export const pdfService = {
  // Gerar PDF do contrato
  async contrato(id) {
    try {
      const response = await api.get(`/pdf/contrato/${id}`, {
        responseType: 'blob'
      })
      
      // Criar URL do blob e fazer download
      const blob = new Blob([response.data], { type: 'application/pdf' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `contrato_${id}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      return { success: true }
    } catch (error) {
      console.error('Erro ao gerar PDF do contrato:', error)
      throw error
    }
  },

  // Gerar PDF do recibo
  async recibo(id) {
    try {
      const response = await api.get(`/pdf/recibo/${id}`, {
        responseType: 'blob'
      })
      
      const blob = new Blob([response.data], { type: 'application/pdf' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `recibo_${id}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      return { success: true }
    } catch (error) {
      console.error('Erro ao gerar PDF do recibo:', error)
      throw error
    }
  },

  // Gerar PDF de solicitação de orçamento
  async solicitacao(dados) {
    try {
      const response = await api.post('/pdf/solicitacao', dados, {
        responseType: 'blob'
      })
      
      const blob = new Blob([response.data], { type: 'application/pdf' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `solicitacao_orcamento.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      return { success: true }
    } catch (error) {
      console.error('Erro ao gerar PDF da solicitação:', error)
      throw error
    }
  },

  // Gerar relatório de contas a receber
  async relatorioContasReceber(filtros = {}) {
    try {
      const response = await api.get('/pdf/relatorio/contas-receber', {
        params: filtros,
        responseType: 'blob'
      })
      
      const blob = new Blob([response.data], { type: 'application/pdf' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `relatorio_contas_receber_${new Date().toISOString().split('T')[0]}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      return { success: true }
    } catch (error) {
      console.error('Erro ao gerar relatório:', error)
      throw error
    }
  },

  // Gerar relatório de equipamentos
  async relatorioEquipamentos(filtros = {}) {
    try {
      const response = await api.get('/pdf/relatorio/equipamentos', {
        params: filtros,
        responseType: 'blob'
      })
      
      const blob = new Blob([response.data], { type: 'application/pdf' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `relatorio_equipamentos_${new Date().toISOString().split('T')[0]}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      return { success: true }
    } catch (error) {
      console.error('Erro ao gerar relatório:', error)
      throw error
    }
  }
}
