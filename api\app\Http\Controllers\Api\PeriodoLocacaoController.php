<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PeriodoLocacao;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class PeriodoLocacaoController extends Controller
{
    /**
     * Listar períodos de locação
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = PeriodoLocacao::query();

            // Filtros
            if ($request->has('buscar') && $request->buscar) {
                $query->buscar($request->buscar);
            }

            if ($request->has('ativo')) {
                if ($request->boolean('ativo')) {
                    $query->ativos();
                } else {
                    $query->where('is_ativo', false);
                }
            }

            // Ordenação
            $orderBy = $request->get('order_by', 'ordem');
            $orderDirection = $request->get('order_direction', 'asc');
            
            if ($orderBy === 'ordem') {
                $query->ordenados();
            } else {
                $query->orderBy($orderBy, $orderDirection);
            }

            // Paginação
            $perPage = $request->get('per_page', 15);
            $periodos = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $periodos
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao listar períodos de locação',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Criar período de locação
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'nome' => 'required|string|max:100',
                'descricao' => 'nullable|string',
                'dias_minimo' => 'required|integer|min:1',
                'dias_maximo' => 'nullable|integer|min:1|gte:dias_minimo',
                'multiplicador_preco' => 'required|numeric|min:0.0001|max:9999.9999',
                'desconto_percentual' => 'nullable|numeric|min:0|max:100',
                'is_ativo' => 'boolean',
                'ordem' => 'nullable|integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Dados inválidos',
                    'errors' => $validator->errors()
                ], 422);
            }

            $periodo = PeriodoLocacao::create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Período de locação criado com sucesso',
                'data' => $periodo
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao criar período de locação',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Exibir período de locação específico
     */
    public function show(string $id): JsonResponse
    {
        try {
            $periodo = PeriodoLocacao::with(['contratos'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $periodo
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Período de locação não encontrado',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Atualizar período de locação
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $periodo = PeriodoLocacao::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'nome' => 'required|string|max:100',
                'descricao' => 'nullable|string',
                'dias_minimo' => 'required|integer|min:1',
                'dias_maximo' => 'nullable|integer|min:1|gte:dias_minimo',
                'multiplicador_preco' => 'required|numeric|min:0.0001|max:9999.9999',
                'desconto_percentual' => 'nullable|numeric|min:0|max:100',
                'is_ativo' => 'boolean',
                'ordem' => 'nullable|integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Dados inválidos',
                    'errors' => $validator->errors()
                ], 422);
            }

            $periodo->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Período de locação atualizado com sucesso',
                'data' => $periodo
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao atualizar período de locação',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remover período de locação
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $periodo = PeriodoLocacao::findOrFail($id);

            // Verificar se há contratos usando este período
            if ($periodo->contratos()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Não é possível excluir este período pois há contratos vinculados a ele'
                ], 422);
            }

            $periodo->delete();

            return response()->json([
                'success' => true,
                'message' => 'Período de locação removido com sucesso'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao remover período de locação',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Ativar período de locação
     */
    public function ativar(string $id): JsonResponse
    {
        try {
            $periodo = PeriodoLocacao::findOrFail($id);
            $periodo->ativar();

            return response()->json([
                'success' => true,
                'message' => 'Período de locação ativado com sucesso',
                'data' => $periodo
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao ativar período de locação',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Desativar período de locação
     */
    public function desativar(string $id): JsonResponse
    {
        try {
            $periodo = PeriodoLocacao::findOrFail($id);
            $periodo->desativar();

            return response()->json([
                'success' => true,
                'message' => 'Período de locação desativado com sucesso',
                'data' => $periodo
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao desativar período de locação',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reordenar períodos de locação
     */
    public function reordenar(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'periodos' => 'required|array',
                'periodos.*.id' => 'required|exists:periodos_locacao,id',
                'periodos.*.ordem' => 'required|integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Dados inválidos',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            foreach ($request->periodos as $item) {
                PeriodoLocacao::where('id', $item['id'])
                    ->update(['ordem' => $item['ordem']]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Períodos reordenados com sucesso'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Erro ao reordenar períodos',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obter período adequado para um número de dias
     */
    public function obterPeriodoParaDias(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'dias' => 'required|integer|min:1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Dados inválidos',
                    'errors' => $validator->errors()
                ], 422);
            }

            $periodo = PeriodoLocacao::obterPeriodoParaDias($request->dias);

            return response()->json([
                'success' => true,
                'data' => $periodo
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao obter período',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
