<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Contrato;
use App\Models\ContaReceber;
use App\Models\ContaPagar;
use App\Models\Recibo;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Barryvdh\DomPDF\Facade\Pdf;

class PdfController extends Controller
{
    /**
     * Gerar PDF do contrato
     */
    public function contrato(string $id)
    {
        try {
            $contrato = Contrato::with([
                'cliente', 
                'itens.equipamento.categoria'
            ])->findOrFail($id);

            $pdf = Pdf::loadView('pdfs.contrato', compact('contrato'));
            
            $filename = "contrato_{$contrato->numero_contrato}.pdf";
            
            return $pdf->download($filename);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao gerar PDF do contrato',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Gerar PDF do recibo
     */
    public function recibo(string $id)
    {
        try {
            $recibo = Recibo::with([
                'contaReceber.cliente',
                'contaPagar'
            ])->findOrFail($id);

            $pdf = Pdf::loadView('pdfs.recibo', compact('recibo'));
            
            $filename = "recibo_{$recibo->numero_recibo}.pdf";
            
            return $pdf->download($filename);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao gerar PDF do recibo',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Gerar PDF de solicitação de orçamento
     */
    public function solicitacao(Request $request)
    {
        try {
            $dados = $request->validate([
                'cliente_nome' => 'required|string',
                'cliente_email' => 'required|email',
                'cliente_telefone' => 'required|string',
                'equipamentos' => 'required|array',
                'equipamentos.*.id' => 'required|exists:equipamentos,id',
                'equipamentos.*.quantidade' => 'required|integer|min:1',
                'equipamentos.*.dias' => 'required|integer|min:1',
                'data_inicio' => 'required|date',
                'data_fim' => 'required|date|after:data_inicio',
                'observacoes' => 'nullable|string'
            ]);

            // Buscar dados dos equipamentos
            $equipamentosIds = collect($dados['equipamentos'])->pluck('id');
            $equipamentos = \App\Models\Equipamento::with('categoria')
                ->whereIn('id', $equipamentosIds)
                ->get()
                ->keyBy('id');

            // Calcular valores
            $itens = collect($dados['equipamentos'])->map(function($item) use ($equipamentos) {
                $equipamento = $equipamentos[$item['id']];
                $valorUnitario = $equipamento->valor_diario;
                $valorTotal = $item['quantidade'] * $valorUnitario * $item['dias'];
                
                return [
                    'equipamento' => $equipamento,
                    'quantidade' => $item['quantidade'],
                    'dias' => $item['dias'],
                    'valor_unitario' => $valorUnitario,
                    'valor_total' => $valorTotal
                ];
            });

            $valorTotal = $itens->sum('valor_total');

            $solicitacao = [
                'numero' => 'SOL' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT),
                'data_emissao' => now(),
                'cliente' => $dados,
                'itens' => $itens,
                'valor_total' => $valorTotal,
                'data_inicio' => $dados['data_inicio'],
                'data_fim' => $dados['data_fim'],
                'observacoes' => $dados['observacoes'] ?? ''
            ];

            $pdf = Pdf::loadView('pdfs.solicitacao', compact('solicitacao'));
            
            $filename = "solicitacao_{$solicitacao['numero']}.pdf";
            
            return $pdf->download($filename);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao gerar PDF da solicitação',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Gerar relatório de contas a receber
     */
    public function relatorioContasReceber(Request $request)
    {
        try {
            $query = ContaReceber::with(['cliente', 'contrato']);

            // Aplicar filtros
            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('data_inicio') && $request->has('data_fim')) {
                $query->porPeriodo($request->data_inicio, $request->data_fim);
            }

            if ($request->has('cliente_id')) {
                $query->where('cliente_id', $request->cliente_id);
            }

            $contas = $query->orderBy('data_vencimento')->get();
            
            $resumo = [
                'total_contas' => $contas->count(),
                'valor_total' => $contas->sum('valor'),
                'pendentes' => $contas->where('status', 'pendente')->count(),
                'valor_pendente' => $contas->where('status', 'pendente')->sum('valor'),
                'pagas' => $contas->where('status', 'pago')->count(),
                'valor_pago' => $contas->where('status', 'pago')->sum('valor'),
                'vencidas' => $contas->where('status', 'vencido')->count(),
                'valor_vencido' => $contas->where('status', 'vencido')->sum('valor')
            ];

            $filtros = $request->only(['status', 'data_inicio', 'data_fim', 'cliente_id']);

            $pdf = Pdf::loadView('pdfs.relatorio-contas-receber', compact('contas', 'resumo', 'filtros'));
            
            $filename = "relatorio_contas_receber_" . now()->format('Y-m-d') . ".pdf";
            
            return $pdf->download($filename);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao gerar relatório',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Gerar relatório de equipamentos
     */
    public function relatorioEquipamentos(Request $request)
    {
        try {
            $query = \App\Models\Equipamento::with(['categoria']);

            // Aplicar filtros
            if ($request->has('categoria_id')) {
                $query->where('categoria_id', $request->categoria_id);
            }

            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            $equipamentos = $query->orderBy('nome')->get();
            
            $resumo = [
                'total_equipamentos' => $equipamentos->count(),
                'disponiveis' => $equipamentos->where('status', 'disponivel')->count(),
                'locados' => $equipamentos->where('quantidade_locada', '>', 0)->count(),
                'manutencao' => $equipamentos->where('status', 'manutencao')->count(),
                'valor_total_diario' => $equipamentos->sum('valor_diario')
            ];

            $filtros = $request->only(['categoria_id', 'status']);

            $pdf = Pdf::loadView('pdfs.relatorio-equipamentos', compact('equipamentos', 'resumo', 'filtros'));
            
            $filename = "relatorio_equipamentos_" . now()->format('Y-m-d') . ".pdf";
            
            return $pdf->download($filename);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao gerar relatório',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
