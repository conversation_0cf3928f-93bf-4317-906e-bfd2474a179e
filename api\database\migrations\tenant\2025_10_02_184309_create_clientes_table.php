<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clientes', function (Blueprint $table) {
            $table->id();
            $table->string('tipo_pessoa', 2); // PF ou PJ
            $table->string('nome_razao_social');
            $table->string('cpf_cnpj', 18)->unique();
            $table->string('rg_ie')->nullable();
            $table->string('email')->nullable();
            $table->string('telefone')->nullable();
            $table->string('celular')->nullable();
            $table->date('data_nascimento')->nullable();

            // Endereço de cobrança
            $table->string('endereco_cobranca_cep', 9);
            $table->string('endereco_cobranca_logradouro');
            $table->string('endereco_cobranca_numero', 10);
            $table->string('endereco_cobranca_complemento')->nullable();
            $table->string('endereco_cobranca_bairro');
            $table->string('endereco_cobranca_cidade');
            $table->string('endereco_cobranca_uf', 2);

            $table->boolean('is_active')->default(true);
            $table->text('observacoes')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['tipo_pessoa', 'is_active']);
            $table->index('nome_razao_social');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clientes');
    }
};
