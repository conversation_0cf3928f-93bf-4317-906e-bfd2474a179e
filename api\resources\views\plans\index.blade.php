<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LocBem - Gerenciar Planos</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Gerenciar Planos</h1>
                        <p class="mt-2 text-gray-600">Configure os planos de assinatura disponíveis</p>
                    </div>
                    <div class="flex space-x-4">
                        <a href="{{ route('tenants.index') }}" 
                           class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            Voltar para Tenants
                        </a>
                        <a href="{{ route('plans.create') }}" 
                           class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Novo Plano
                        </a>
                    </div>
                </div>
            </div>

            <!-- Plans Grid -->
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                @foreach($plans as $plan)
                <div class="bg-white overflow-hidden shadow rounded-lg border-2 {{ $plan['id'] === 'premium' ? 'border-blue-500' : 'border-gray-200' }}">
                    @if($plan['id'] === 'premium')
                    <div class="bg-blue-500 text-white text-center py-2 text-sm font-medium">
                        MAIS POPULAR
                    </div>
                    @endif
                    
                    <div class="px-6 py-8">
                        <div class="text-center">
                            <h3 class="text-2xl font-bold text-gray-900">{{ $plan['name'] }}</h3>
                            <p class="mt-2 text-gray-600">{{ $plan['description'] }}</p>
                            
                            <div class="mt-4">
                                @if($plan['price'] > 0)
                                <span class="text-4xl font-bold text-gray-900">R$ {{ number_format($plan['price'], 2, ',', '.') }}</span>
                                <span class="text-gray-600">/mês</span>
                                @else
                                <span class="text-4xl font-bold text-green-600">Grátis</span>
                                <span class="text-gray-600">por {{ $plan['duration_days'] }} dias</span>
                                @endif
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Recursos inclusos:</h4>
                            <ul class="space-y-2">
                                @foreach($plan['features'] as $feature)
                                <li class="flex items-start">
                                    <svg class="flex-shrink-0 h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="ml-2 text-sm text-gray-700">{{ $feature }}</span>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                        
                        <div class="mt-6">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">Limites:</h4>
                            <div class="grid grid-cols-2 gap-2 text-sm">
                                @foreach($plan['limits'] as $key => $limit)
                                <div class="flex justify-between">
                                    <span class="text-gray-600 capitalize">{{ ucfirst($key) }}:</span>
                                    <span class="font-medium">{{ $limit === -1 ? 'Ilimitado' : $limit }}</span>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        
                        <div class="mt-8 flex space-x-2">
                            <a href="{{ route('plans.edit', $plan['id']) }}" 
                               class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded text-sm font-medium">
                                Editar
                            </a>
                            <a href="{{ route('plans.show', $plan['id']) }}" 
                               class="flex-1 bg-gray-600 hover:bg-gray-700 text-white text-center py-2 px-4 rounded text-sm font-medium">
                                Ver
                            </a>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Statistics -->
            <div class="mt-12 bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Estatísticas dos Planos</h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">Distribuição de tenants por plano</p>
                </div>
                <div class="border-t border-gray-200">
                    <div class="grid grid-cols-1 gap-5 sm:grid-cols-4 p-6">
                        @foreach($plans as $plan)
                        <div class="bg-gray-50 overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-{{ $plan['id'] === 'trial' ? 'green' : ($plan['id'] === 'basic' ? 'blue' : ($plan['id'] === 'premium' ? 'purple' : 'gray')) }}-500 rounded-full flex items-center justify-center">
                                            <span class="text-white text-sm font-bold">{{ strtoupper(substr($plan['name'], 0, 1)) }}</span>
                                        </div>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">{{ $plan['name'] }}</dt>
                                            <dd class="text-lg font-medium text-gray-900">
                                                {{ \App\Models\Tenant::where('subscription_plan', $plan['id'])->count() }} tenants
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded shadow-lg">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded shadow-lg">
            {{ session('error') }}
        </div>
    @endif

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.fixed.top-4.right-4');
            alerts.forEach(alert => {
                alert.style.display = 'none';
            });
        }, 5000);
    </script>
</body>
</html>
