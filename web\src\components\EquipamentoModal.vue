<template>
  <div v-if="isOpen" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-3xl shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <!-- Header -->
        <div class="flex items-center justify-between pb-4 border-b">
          <h3 class="text-lg font-medium text-gray-900">
            {{ isEditing ? 'Editar Equipamento' : 'Novo Equipamento' }}
          </h3>
          <button @click="fechar" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Form -->
        <form @submit.prevent="salvar" class="mt-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Dados Básicos -->
            <div class="space-y-4">
              <h4 class="text-md font-medium text-gray-900 border-b pb-2">Dados Básicos</h4>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Nome *</label>
                <input
                  v-model="form.nome"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Categoria *</label>
                <select
                  v-model="form.categoria_id"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="">Selecione uma categoria...</option>
                  <option v-for="categoria in categorias" :key="categoria.id" :value="categoria.id">
                    {{ categoria.nome }}
                  </option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Código/Modelo</label>
                <input
                  v-model="form.codigo"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Marca</label>
                <input
                  v-model="form.marca"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Ano</label>
                <input
                  v-model="form.ano"
                  type="number"
                  min="1900"
                  :max="new Date().getFullYear() + 1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div class="flex items-center">
                <input
                  v-model="form.is_active"
                  type="checkbox"
                  id="ativo"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label for="ativo" class="ml-2 block text-sm text-gray-900">Equipamento Ativo</label>
              </div>
            </div>

            <!-- Valores e Status -->
            <div class="space-y-4">
              <h4 class="text-md font-medium text-gray-900 border-b pb-2">Valores e Status</h4>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Valor Diário (R$) *</label>
                <input
                  v-model="form.valor_diario"
                  type="number"
                  step="0.01"
                  min="0"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Valor Semanal (R$)</label>
                <input
                  v-model="form.valor_semanal"
                  type="number"
                  step="0.01"
                  min="0"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Valor Mensal (R$)</label>
                <input
                  v-model="form.valor_mensal"
                  type="number"
                  step="0.01"
                  min="0"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Status *</label>
                <select
                  v-model="form.status"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="">Selecione o status...</option>
                  <option value="disponivel">Disponível</option>
                  <option value="locado">Locado</option>
                  <option value="manutencao">Em Manutenção</option>
                  <option value="inativo">Inativo</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Localização</label>
                <input
                  v-model="form.localizacao"
                  type="text"
                  placeholder="Ex: Galpão A - Setor 1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
            </div>
          </div>

          <!-- Descrição -->
          <div class="mt-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
            <textarea
              v-model="form.descricao"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Descrição detalhada do equipamento..."
            ></textarea>
          </div>

          <!-- Observações -->
          <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Observações</label>
            <textarea
              v-model="form.observacoes"
              rows="2"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Observações adicionais..."
            ></textarea>
          </div>

          <!-- Erro -->
          <div v-if="erro" class="mt-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
            {{ erro }}
          </div>

          <!-- Botões -->
          <div class="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              @click="fechar"
              class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancelar
            </button>
            <button
              type="submit"
              :disabled="salvando"
              class="px-4 py-2 bg-indigo-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-indigo-700 disabled:opacity-50"
            >
              {{ salvando ? 'Salvando...' : 'Salvar' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { equipamentosService, categoriasService } from '../services/equipamentos.js'

export default {
  name: 'EquipamentoModal',
  props: {
    isOpen: {
      type: Boolean,
      default: false
    },
    equipamento: {
      type: Object,
      default: null
    }
  },
  emits: ['close', 'saved'],
  setup(props, { emit }) {
    const salvando = ref(false)
    const erro = ref('')
    const categorias = ref([])

    const form = ref({
      nome: '',
      categoria_id: '',
      codigo: '',
      marca: '',
      ano: '',
      descricao: '',
      valor_diario: '',
      valor_semanal: '',
      valor_mensal: '',
      status: 'disponivel',
      localizacao: '',
      observacoes: '',
      is_active: true
    })

    const isEditing = computed(() => props.equipamento && props.equipamento.id)

    // Carregar categorias
    const carregarCategorias = async () => {
      try {
        const response = await categoriasService.listar()
        if (response.success) {
          categorias.value = response.data
        }
      } catch (error) {
        console.error('Erro ao carregar categorias:', error)
      }
    }

    // Preencher form quando equipamento for passado
    watch(() => props.equipamento, (novoEquipamento) => {
      if (novoEquipamento) {
        Object.keys(form.value).forEach(key => {
          form.value[key] = novoEquipamento[key] || form.value[key]
        })
      } else {
        // Reset form
        Object.keys(form.value).forEach(key => {
          if (key === 'is_active') {
            form.value[key] = true
          } else if (key === 'status') {
            form.value[key] = 'disponivel'
          } else {
            form.value[key] = ''
          }
        })
      }
    }, { immediate: true })

    const salvar = async () => {
      try {
        salvando.value = true
        erro.value = ''

        let resultado
        if (isEditing.value) {
          resultado = await equipamentosService.atualizar(props.equipamento.id, form.value)
        } else {
          resultado = await equipamentosService.criar(form.value)
        }

        if (resultado.success) {
          emit('saved', resultado.data)
          fechar()
        } else {
          erro.value = resultado.message || 'Erro ao salvar equipamento'
        }
      } catch (error) {
        console.error('Erro ao salvar equipamento:', error)
        erro.value = error.response?.data?.message || 'Erro ao salvar equipamento'
      } finally {
        salvando.value = false
      }
    }

    const fechar = () => {
      emit('close')
    }

    onMounted(() => {
      carregarCategorias()
    })

    return {
      form,
      salvando,
      erro,
      categorias,
      isEditing,
      salvar,
      fechar
    }
  }
}
</script>
