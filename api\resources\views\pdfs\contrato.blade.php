@extends('pdfs.layout')

@section('title', 'Contrato de Locação - ' . $contrato->numero_contrato)

@section('content')
<div class="document-title">
    CONTRATO DE LOCAÇÃO DE EQUIPAMENTOS
</div>

<div class="document-info">
    <div class="left">
        <div class="info-group">
            <div class="info-label">Número do Contrato:</div>
            <div class="info-value font-bold text-blue">#{{ $contrato->numero_contrato }}</div>
        </div>
        
        <div class="info-group">
            <div class="info-label">Data de Início:</div>
            <div class="info-value">{{ $contrato->data_inicio->format('d/m/Y') }}</div>
        </div>
        
        <div class="info-group">
            <div class="info-label">Data de Fim:</div>
            <div class="info-value">{{ $contrato->data_fim->format('d/m/Y') }}</div>
        </div>
        
        <div class="info-group">
            <div class="info-label">Previsão de Devolução:</div>
            <div class="info-value">{{ $contrato->data_prevista_devolucao->format('d/m/Y') }}</div>
        </div>
    </div>
    
    <div class="right">
        <div class="info-group">
            <div class="info-label">Status:</div>
            <div class="info-value font-bold 
                @if($contrato->status === 'ativo') text-green
                @elseif($contrato->status === 'pendente') text-yellow
                @elseif($contrato->status === 'finalizado') text-blue
                @else text-red
                @endif">
                {{ ucfirst($contrato->status) }}
            </div>
        </div>
        
        <div class="info-group">
            <div class="info-label">Data de Emissão:</div>
            <div class="info-value">{{ $contrato->created_at->format('d/m/Y H:i') }}</div>
        </div>
        
        @if($contrato->data_devolucao_real)
        <div class="info-group">
            <div class="info-label">Data de Devolução Real:</div>
            <div class="info-value">{{ $contrato->data_devolucao_real->format('d/m/Y') }}</div>
        </div>
        @endif
    </div>
</div>

<div class="summary-box">
    <div class="summary-title">DADOS DO CLIENTE</div>
    <div class="summary-item">
        <div class="summary-label">Nome/Razão Social:</div>
        <div class="summary-value">{{ $contrato->cliente->nome_razao_social }}</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">CPF/CNPJ:</div>
        <div class="summary-value">{{ $contrato->cliente->cpf_cnpj }}</div>
    </div>
    @if($contrato->cliente->rg_ie)
    <div class="summary-item">
        <div class="summary-label">RG/IE:</div>
        <div class="summary-value">{{ $contrato->cliente->rg_ie }}</div>
    </div>
    @endif
    <div class="summary-item">
        <div class="summary-label">Email:</div>
        <div class="summary-value">{{ $contrato->cliente->email }}</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">Telefone:</div>
        <div class="summary-value">{{ $contrato->cliente->telefone }}</div>
    </div>
</div>

<div class="summary-box">
    <div class="summary-title">ENDEREÇO DE ENTREGA</div>
    <div class="summary-item">
        <div class="summary-label">Logradouro:</div>
        <div class="summary-value">{{ $contrato->endereco_entrega_logradouro }}, {{ $contrato->endereco_entrega_numero }}</div>
    </div>
    @if($contrato->endereco_entrega_complemento)
    <div class="summary-item">
        <div class="summary-label">Complemento:</div>
        <div class="summary-value">{{ $contrato->endereco_entrega_complemento }}</div>
    </div>
    @endif
    <div class="summary-item">
        <div class="summary-label">Bairro:</div>
        <div class="summary-value">{{ $contrato->endereco_entrega_bairro }}</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">Cidade/UF:</div>
        <div class="summary-value">{{ $contrato->endereco_entrega_cidade }}/{{ $contrato->endereco_entrega_uf }}</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">CEP:</div>
        <div class="summary-value">{{ $contrato->endereco_entrega_cep }}</div>
    </div>
</div>

<div class="document-title" style="font-size: 16px; margin: 30px 0 20px;">
    EQUIPAMENTOS LOCADOS
</div>

<table class="table">
    <thead>
        <tr>
            <th>Equipamento</th>
            <th>Categoria</th>
            <th class="text-center">Qtd</th>
            <th class="text-center">Dias</th>
            <th class="text-right">Valor Unit.</th>
            <th class="text-right">Valor Total</th>
        </tr>
    </thead>
    <tbody>
        @foreach($contrato->itens as $item)
        <tr>
            <td>
                <strong>{{ $item->equipamento->nome }}</strong><br>
                <small>{{ $item->equipamento->codigo_sku }}</small>
                @if($item->observacoes)
                <br><small><em>{{ $item->observacoes }}</em></small>
                @endif
            </td>
            <td>{{ $item->equipamento->categoria->nome }}</td>
            <td class="text-center">{{ $item->quantidade }}</td>
            <td class="text-center">{{ $item->dias_locacao }}</td>
            <td class="text-right">R$ {{ number_format($item->valor_unitario, 2, ',', '.') }}</td>
            <td class="text-right font-bold">R$ {{ number_format($item->valor_total, 2, ',', '.') }}</td>
        </tr>
        @endforeach
    </tbody>
</table>

<div class="summary-box">
    <div class="summary-title">RESUMO FINANCEIRO</div>
    <div class="summary-item">
        <div class="summary-label">Valor Total dos Equipamentos:</div>
        <div class="summary-value">R$ {{ number_format($contrato->valor_total, 2, ',', '.') }}</div>
    </div>
    @if($contrato->valor_desconto > 0)
    <div class="summary-item">
        <div class="summary-label">Desconto:</div>
        <div class="summary-value text-red">- R$ {{ number_format($contrato->valor_desconto, 2, ',', '.') }}</div>
    </div>
    @endif
    <div class="summary-item" style="border-top: 1px solid #333; padding-top: 5px; margin-top: 10px;">
        <div class="summary-label font-bold">VALOR FINAL:</div>
        <div class="summary-value font-bold text-blue" style="font-size: 16px;">R$ {{ number_format($contrato->valor_final, 2, ',', '.') }}</div>
    </div>
</div>

@if($contrato->observacoes)
<div class="summary-box">
    <div class="summary-title">OBSERVAÇÕES</div>
    <div style="text-align: justify;">{{ $contrato->observacoes }}</div>
</div>
@endif

@if($contrato->condicoes_especiais)
<div class="summary-box">
    <div class="summary-title">CONDIÇÕES ESPECIAIS</div>
    <div style="text-align: justify;">{{ $contrato->condicoes_especiais }}</div>
</div>
@endif

<div class="summary-box">
    <div class="summary-title">TERMOS E CONDIÇÕES</div>
    <div style="text-align: justify; font-size: 11px; line-height: 1.3;">
        1. O locatário se compromete a devolver os equipamentos nas mesmas condições em que foram entregues.<br>
        2. Qualquer dano ou perda será cobrado do locatário pelo valor de mercado do equipamento.<br>
        3. O pagamento deverá ser efetuado conforme acordado no momento da locação.<br>
        4. A devolução após o prazo estipulado acarretará em cobrança de diárias adicionais.<br>
        5. É de responsabilidade do locatário o transporte dos equipamentos, salvo acordo em contrário.<br>
        6. Este contrato é regido pelas leis brasileiras e fica eleito o foro da comarca de São Paulo/SP.
    </div>
</div>

<div class="signatures">
    <div class="signature-left">
        <div class="signature-line">LocBem Equipamentos</div>
        <div>Locador</div>
    </div>
    <div class="signature-right">
        <div class="signature-line">{{ $contrato->cliente->nome_razao_social }}</div>
        <div>Locatário</div>
    </div>
</div>
@endsection
