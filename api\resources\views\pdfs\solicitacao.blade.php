@extends('pdfs.layout')

@section('title', 'Solicitação de Orçamento - ' . $solicitacao['numero'])

@section('content')
<div class="document-title">
    SOLICITAÇÃO DE ORÇAMENTO
</div>

<div class="document-info">
    <div class="left">
        <div class="info-group">
            <div class="info-label">Número da Solicitação:</div>
            <div class="info-value font-bold text-blue">#{{ $solicitacao['numero'] }}</div>
        </div>
        
        <div class="info-group">
            <div class="info-label">Data de Emissão:</div>
            <div class="info-value">{{ $solicitacao['data_emissao']->format('d/m/Y H:i') }}</div>
        </div>
        
        <div class="info-group">
            <div class="info-label">Período de Locação:</div>
            <div class="info-value">
                {{ \Carbon\Carbon::parse($solicitacao['data_inicio'])->format('d/m/Y') }} até 
                {{ \Carbon\Carbon::parse($solicitacao['data_fim'])->format('d/m/Y') }}
            </div>
        </div>
    </div>
    
    <div class="right">
        <div class="info-group">
            <div class="info-label">Validade da Proposta:</div>
            <div class="info-value">{{ now()->addDays(15)->format('d/m/Y') }}</div>
        </div>
        
        <div class="info-group">
            <div class="info-label">Status:</div>
            <div class="info-value font-bold text-yellow">Aguardando Aprovação</div>
        </div>
    </div>
</div>

<div class="summary-box">
    <div class="summary-title">DADOS DO SOLICITANTE</div>
    <div class="summary-item">
        <div class="summary-label">Nome:</div>
        <div class="summary-value">{{ $solicitacao['cliente']['cliente_nome'] }}</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">Email:</div>
        <div class="summary-value">{{ $solicitacao['cliente']['cliente_email'] }}</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">Telefone:</div>
        <div class="summary-value">{{ $solicitacao['cliente']['cliente_telefone'] }}</div>
    </div>
</div>

<div class="document-title" style="font-size: 16px; margin: 30px 0 20px;">
    EQUIPAMENTOS SOLICITADOS
</div>

<table class="table">
    <thead>
        <tr>
            <th>Equipamento</th>
            <th>Categoria</th>
            <th class="text-center">Qtd</th>
            <th class="text-center">Dias</th>
            <th class="text-right">Valor Diário</th>
            <th class="text-right">Valor Total</th>
        </tr>
    </thead>
    <tbody>
        @foreach($solicitacao['itens'] as $item)
        <tr>
            <td>
                <strong>{{ $item['equipamento']->nome }}</strong><br>
                <small>{{ $item['equipamento']->codigo_sku }}</small>
                @if($item['equipamento']->descricao)
                <br><small style="color: #666;">{{ Str::limit($item['equipamento']->descricao, 60) }}</small>
                @endif
            </td>
            <td>{{ $item['equipamento']->categoria->nome }}</td>
            <td class="text-center">{{ $item['quantidade'] }}</td>
            <td class="text-center">{{ $item['dias'] }}</td>
            <td class="text-right">R$ {{ number_format($item['valor_unitario'], 2, ',', '.') }}</td>
            <td class="text-right font-bold">R$ {{ number_format($item['valor_total'], 2, ',', '.') }}</td>
        </tr>
        @endforeach
    </tbody>
</table>

<div class="summary-box">
    <div class="summary-title">RESUMO DO ORÇAMENTO</div>
    <div class="summary-item">
        <div class="summary-label">Total de Equipamentos:</div>
        <div class="summary-value">{{ $solicitacao['itens']->count() }} item(s)</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">Total de Dias:</div>
        <div class="summary-value">{{ \Carbon\Carbon::parse($solicitacao['data_inicio'])->diffInDays(\Carbon\Carbon::parse($solicitacao['data_fim'])) + 1 }} dia(s)</div>
    </div>
    <div class="summary-item" style="border-top: 1px solid #333; padding-top: 5px; margin-top: 10px;">
        <div class="summary-label font-bold">VALOR TOTAL:</div>
        <div class="summary-value font-bold text-blue" style="font-size: 18px;">R$ {{ number_format($solicitacao['valor_total'], 2, ',', '.') }}</div>
    </div>
</div>

@if($solicitacao['observacoes'])
<div class="summary-box">
    <div class="summary-title">OBSERVAÇÕES DO CLIENTE</div>
    <div style="text-align: justify;">{{ $solicitacao['observacoes'] }}</div>
</div>
@endif

<div class="summary-box">
    <div class="summary-title">CONDIÇÕES COMERCIAIS</div>
    <div style="text-align: justify; font-size: 11px; line-height: 1.4;">
        <strong>Forma de Pagamento:</strong> À vista ou parcelado conforme negociação.<br>
        <strong>Entrega:</strong> Por conta do cliente, salvo acordo em contrário.<br>
        <strong>Retirada:</strong> Por conta do cliente, salvo acordo em contrário.<br>
        <strong>Caução:</strong> Poderá ser solicitada caução equivalente a 30% do valor total.<br>
        <strong>Validade:</strong> Esta proposta é válida por 15 dias a partir da data de emissão.<br>
        <strong>Observações:</strong> Valores sujeitos a alteração sem aviso prévio. Equipamentos sujeitos à disponibilidade.
    </div>
</div>

<div class="summary-box">
    <div class="summary-title">TERMOS E CONDIÇÕES</div>
    <div style="text-align: justify; font-size: 10px; line-height: 1.3;">
        1. Os equipamentos deverão ser devolvidos nas mesmas condições em que foram entregues.<br>
        2. Qualquer dano ou perda será cobrado pelo valor de mercado do equipamento.<br>
        3. O locatário é responsável pelo transporte, seguro e manutenção durante o período de locação.<br>
        4. A devolução após o prazo estipulado acarretará em cobrança de diárias adicionais.<br>
        5. É obrigatório o uso de EPI adequado para operação dos equipamentos.<br>
        6. O locatário deve possuir operadores habilitados para os equipamentos solicitados.<br>
        7. Esta proposta não constitui reserva de equipamentos até sua aprovação formal.<br>
        8. Foro eleito: Comarca de São Paulo/SP.
    </div>
</div>

<div style="text-align: center; margin-top: 40px; padding: 20px; border: 2px solid #2563eb; border-radius: 8px;">
    <div style="font-size: 16px; font-weight: bold; color: #2563eb; margin-bottom: 10px;">
        PARA APROVAÇÃO DESTA PROPOSTA
    </div>
    <div style="font-size: 12px; line-height: 1.4;">
        Entre em contato conosco através dos canais:<br>
        <strong>Telefone:</strong> (11) 1234-5678<br>
        <strong>Email:</strong> <EMAIL><br>
        <strong>WhatsApp:</strong> (11) 91234-5678
    </div>
</div>

<div style="text-align: center; margin-top: 30px;">
    <div class="signature-line" style="width: 300px; margin: 0 auto;">
        LocBem Equipamentos
    </div>
    <div style="margin-top: 5px; font-size: 11px;">
        Departamento Comercial
    </div>
</div>
@endsection
