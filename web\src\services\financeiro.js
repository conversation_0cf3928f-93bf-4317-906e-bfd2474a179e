import api from './api.js'

export const financeiroService = {
  // Dashboard Financeiro
  async dashboard() {
    try {
      const response = await api.get('/financeiro/dashboard')
      return response.data
    } catch (error) {
      console.error('Erro ao carregar dashboard financeiro:', error)
      throw error
    }
  },

  // Contas a Receber
  async contasReceber(params = {}) {
    try {
      const response = await api.get('/financeiro/contas-receber', { params })
      return response.data
    } catch (error) {
      console.error('Erro ao listar contas a receber:', error)
      throw error
    }
  },

  async criarContaReceber(conta) {
    try {
      const response = await api.post('/financeiro/contas-receber', conta)
      return response.data
    } catch (error) {
      console.error('Erro ao criar conta a receber:', error)
      throw error
    }
  },

  async pagarContaReceber(id, pagamento) {
    try {
      const response = await api.post(`/financeiro/contas-receber/${id}/pagar`, pagamento)
      return response.data
    } catch (error) {
      console.error('Erro ao registrar pagamento:', error)
      throw error
    }
  },

  // Contas a Pagar
  async contasPagar(params = {}) {
    try {
      const response = await api.get('/financeiro/contas-pagar', { params })
      return response.data
    } catch (error) {
      console.error('Erro ao listar contas a pagar:', error)
      throw error
    }
  },

  async criarContaPagar(conta) {
    try {
      const response = await api.post('/financeiro/contas-pagar', conta)
      return response.data
    } catch (error) {
      console.error('Erro ao criar conta a pagar:', error)
      throw error
    }
  },

  async pagarContaPagar(id, pagamento) {
    try {
      const response = await api.post(`/financeiro/contas-pagar/${id}/pagar`, pagamento)
      return response.data
    } catch (error) {
      console.error('Erro ao registrar pagamento:', error)
      throw error
    }
  },

  // Recibos
  async recibos(params = {}) {
    try {
      const response = await api.get('/financeiro/recibos', { params })
      return response.data
    } catch (error) {
      console.error('Erro ao listar recibos:', error)
      throw error
    }
  },

  // Categorias
  async categorias() {
    try {
      const response = await api.get('/financeiro/categorias')
      return response.data
    } catch (error) {
      console.error('Erro ao listar categorias:', error)
      throw error
    }
  }
}
