import { defineStore } from 'pinia'
import { authService } from '../services/auth.js'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isAuthenticated: false,
    loading: false,
    initializing: true
  }),

  getters: {
    hasPermission: (state) => (permission) => {
      return state.user?.permissions?.includes(permission) || false
    },

    hasRole: (state) => (role) => {
      return state.user?.roles?.includes(role) || false
    },

    isAdmin: (state) => {
      return state.user?.roles?.includes('admin') || false
    }
  },

  actions: {
    // Inicializar store com dados do localStorage
    async initialize() {
      this.initializing = true

      try {
        const token = authService.getToken()
        const user = authService.getUser()

        if (token && user) {
          this.token = token
          this.user = user
          this.isAuthenticated = true

          // Sempre tentar atualizar os dados do usuário ao inicializar
          // para garantir que as permissions estejam atualizadas
          console.log('Inicializando e atualizando dados do usuário...')
          await this.forceRefreshUser()
        }
      } catch (error) {
        console.error('Erro na inicialização:', error)
      } finally {
        this.initializing = false
      }
    },

    // Login
    async login(credentials) {
      this.loading = true

      try {
        const result = await authService.login(credentials)

        if (result.success) {
          this.token = result.data.token
          this.user = result.data.user
          this.isAuthenticated = true
          this.initializing = false // Marcar como inicializado após login

          // Garantir que as permissões estejam atualizadas após o login
          await this.forceRefreshUser()
        }

        return result
      } finally {
        this.loading = false
      }
    },

    // Logout
    async logout() {
      this.loading = true
      
      try {
        await authService.logout()
      } finally {
        this.token = null
        this.user = null
        this.isAuthenticated = false
        this.loading = false
      }
    },

    // Atualizar dados do usuário
    async refreshUser() {
      if (!this.isAuthenticated) return

      const result = await authService.me()

      if (result.success) {
        this.user = result.data
        // Atualizar também no localStorage
        localStorage.setItem('user_data', JSON.stringify(result.data))
      } else {
        // Se falhar, fazer logout
        await this.logout()
      }
    },

    // Forçar atualização dos dados do usuário (útil quando permissions mudam)
    async forceRefreshUser() {
      if (!this.isAuthenticated) return false

      try {
        const result = await authService.me()

        if (result.success) {
          this.user = result.data
          // Atualizar também no localStorage
          localStorage.setItem('user_data', JSON.stringify(result.data))
          return true
        } else {
          // Se falhar, fazer logout
          await this.logout()
          return false
        }
      } catch (error) {
        console.error('Erro ao atualizar dados do usuário:', error)
        return false
      }
    }
  }
})
