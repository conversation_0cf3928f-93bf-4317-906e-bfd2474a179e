<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\TenantController;

/*
|--------------------------------------------------------------------------
| Central API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register central API routes for your application.
| These routes are loaded by the RouteServiceProvider and are not tenant-specific.
|
*/

// Rota de teste central
Route::get('/test', function () {
    return response()->json([
        'message' => 'API Central LocBem funcionando!',
        'timestamp' => now(),
        'version' => '1.0.0',
        'type' => 'central'
    ]);
});

// Rotas de autenticação (centrais)
Route::group(['prefix' => 'auth'], function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
    Route::post('logout', [AuthController::class, 'logout'])->middleware('auth:api');
    Route::post('refresh', [AuthController::class, 'refresh'])->middleware('auth:api');
    Route::get('me', [AuthController::class, 'me'])->middleware('auth:api');
});

// Rotas de gerenciamento de tenants (apenas para super admin)
Route::middleware(['auth:api'])->prefix('tenants')->group(function () {
    Route::get('/', [TenantController::class, 'index']);
    Route::post('/', [TenantController::class, 'store']);
    Route::get('/stats', [TenantController::class, 'stats']);
    Route::get('/{id}', [TenantController::class, 'show']);
    Route::put('/{id}', [TenantController::class, 'update']);
    Route::delete('/{id}', [TenantController::class, 'destroy']);
});

// Rota de teste protegida central
Route::middleware(['auth:api'])->get('/protected-test', function () {
    return response()->json([
        'message' => 'Rota central protegida funcionando!',
        'user' => auth()->user(),
        'timestamp' => now()
    ]);
});
