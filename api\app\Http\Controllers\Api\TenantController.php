<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Stancl\Tenancy\Database\Models\Domain;

class TenantController extends Controller
{
    /**
     * Display a listing of tenants.
     */
    public function index(): JsonResponse
    {
        $tenants = Tenant::with('domains')->paginate(15);
        
        return response()->json([
            'success' => true,
            'data' => $tenants
        ]);
    }

    /**
     * Store a newly created tenant.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:tenants,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:2',
            'zip_code' => 'nullable|string|max:10',
            'cnpj' => 'nullable|string|max:18|unique:tenants,cnpj',
            'domain' => 'required|string|unique:domains,domain',
            'subscription_plan' => 'nullable|string|in:trial,basic,premium,enterprise',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dados inválidos',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create tenant
            $tenant = Tenant::create([
                'id' => Str::uuid(),
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'zip_code' => $request->zip_code,
                'cnpj' => $request->cnpj,
                'subscription_plan' => $request->subscription_plan ?? 'trial',
                'subscription_status' => 'active',
                'trial_ends_at' => now()->addDays(30),
            ]);

            // Create domain
            $tenant->domains()->create([
                'domain' => $request->domain,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Tenant criado com sucesso',
                'data' => $tenant->load('domains')
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao criar tenant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified tenant.
     */
    public function show(string $id): JsonResponse
    {
        $tenant = Tenant::with('domains')->find($id);

        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant não encontrado'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $tenant
        ]);
    }

    /**
     * Update the specified tenant.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $tenant = Tenant::find($id);

        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant não encontrado'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|email|unique:tenants,email,' . $id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:2',
            'zip_code' => 'nullable|string|max:10',
            'cnpj' => 'nullable|string|max:18|unique:tenants,cnpj,' . $id,
            'subscription_plan' => 'nullable|string|in:trial,basic,premium,enterprise',
            'subscription_status' => 'nullable|string|in:active,suspended,cancelled',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dados inválidos',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenant->update($request->only([
                'name', 'email', 'phone', 'address', 'city', 'state', 
                'zip_code', 'cnpj', 'subscription_plan', 'subscription_status'
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Tenant atualizado com sucesso',
                'data' => $tenant->load('domains')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao atualizar tenant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified tenant.
     */
    public function destroy(string $id): JsonResponse
    {
        $tenant = Tenant::find($id);

        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant não encontrado'
            ], 404);
        }

        try {
            $tenant->delete();

            return response()->json([
                'success' => true,
                'message' => 'Tenant removido com sucesso'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao remover tenant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tenant statistics.
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'total_tenants' => Tenant::count(),
            'active_tenants' => Tenant::where('subscription_status', 'active')->count(),
            'trial_tenants' => Tenant::where('subscription_plan', 'trial')->count(),
            'suspended_tenants' => Tenant::where('subscription_status', 'suspended')->count(),
            'tenants_by_plan' => Tenant::selectRaw('subscription_plan, COUNT(*) as count')
                ->groupBy('subscription_plan')
                ->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
