@extends('pdfs.layout')

@section('title', 'Recibo - ' . $recibo->numero_recibo)

@section('content')
<div class="document-title">
    RECIBO DE {{ $recibo->tipo === 'entrada' ? 'PAGAMENTO' : 'DESPESA' }}
</div>

<div class="document-info">
    <div class="left">
        <div class="info-group">
            <div class="info-label">Número do Recibo:</div>
            <div class="info-value font-bold text-blue">#{{ $recibo->numero_recibo }}</div>
        </div>
        
        <div class="info-group">
            <div class="info-label">Data de Emissão:</div>
            <div class="info-value">{{ $recibo->data_emissao->format('d/m/Y') }}</div>
        </div>
        
        <div class="info-group">
            <div class="info-label">Tipo:</div>
            <div class="info-value font-bold 
                @if($recibo->tipo === 'entrada') text-green
                @else text-red
                @endif">
                {{ $recibo->tipo === 'entrada' ? 'Receita' : 'Despesa' }}
            </div>
        </div>
    </div>
    
    <div class="right">
        <div class="info-group">
            <div class="info-label">{{ $recibo->tipo === 'entrada' ? 'Pagador' : 'Beneficiário' }}:</div>
            <div class="info-value font-bold">{{ $recibo->pessoa_empresa }}</div>
        </div>
        
        <div class="info-group">
            <div class="info-label">Valor:</div>
            <div class="info-value font-bold text-blue" style="font-size: 16px;">
                R$ {{ number_format($recibo->valor, 2, ',', '.') }}
            </div>
        </div>
    </div>
</div>

<div class="summary-box">
    <div class="summary-title">DESCRIÇÃO</div>
    <div style="text-align: justify; font-size: 14px; padding: 10px 0;">
        {{ $recibo->descricao }}
    </div>
</div>

@if($recibo->contaReceber)
<div class="summary-box">
    <div class="summary-title">DADOS DA CONTA A RECEBER</div>
    <div class="summary-item">
        <div class="summary-label">Cliente:</div>
        <div class="summary-value">{{ $recibo->contaReceber->cliente->nome_razao_social ?? 'N/A' }}</div>
    </div>
    @if($recibo->contaReceber->contrato)
    <div class="summary-item">
        <div class="summary-label">Contrato:</div>
        <div class="summary-value">#{{ $recibo->contaReceber->contrato->numero_contrato }}</div>
    </div>
    @endif
    <div class="summary-item">
        <div class="summary-label">Data de Vencimento:</div>
        <div class="summary-value">{{ $recibo->contaReceber->data_vencimento->format('d/m/Y') }}</div>
    </div>
    @if($recibo->contaReceber->forma_pagamento)
    <div class="summary-item">
        <div class="summary-label">Forma de Pagamento:</div>
        <div class="summary-value">{{ $recibo->contaReceber->forma_pagamento }}</div>
    </div>
    @endif
</div>
@endif

@if($recibo->contaPagar)
<div class="summary-box">
    <div class="summary-title">DADOS DA CONTA A PAGAR</div>
    <div class="summary-item">
        <div class="summary-label">Fornecedor:</div>
        <div class="summary-value">{{ $recibo->contaPagar->fornecedor ?? 'N/A' }}</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">Categoria:</div>
        <div class="summary-value">{{ ucfirst($recibo->contaPagar->categoria) }}</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">Data de Vencimento:</div>
        <div class="summary-value">{{ $recibo->contaPagar->data_vencimento->format('d/m/Y') }}</div>
    </div>
    @if($recibo->contaPagar->forma_pagamento)
    <div class="summary-item">
        <div class="summary-label">Forma de Pagamento:</div>
        <div class="summary-value">{{ $recibo->contaPagar->forma_pagamento }}</div>
    </div>
    @endif
</div>
@endif

<div class="summary-box" style="text-align: center; font-size: 14px; margin-top: 40px;">
    <div style="margin-bottom: 20px;">
        @if($recibo->tipo === 'entrada')
            <strong>RECEBI</strong> de <strong>{{ $recibo->pessoa_empresa }}</strong> a importância de 
            <strong>R$ {{ number_format($recibo->valor, 2, ',', '.') }}</strong> 
            ({{ $this->valorPorExtenso($recibo->valor) }}) 
            referente a {{ strtolower($recibo->descricao) }}.
        @else
            <strong>PAGUEI</strong> a <strong>{{ $recibo->pessoa_empresa }}</strong> a importância de 
            <strong>R$ {{ number_format($recibo->valor, 2, ',', '.') }}</strong> 
            ({{ $this->valorPorExtenso($recibo->valor) }}) 
            referente a {{ strtolower($recibo->descricao) }}.
        @endif
    </div>
    
    <div style="margin-top: 30px;">
        Para clareza firmo o presente recibo.
    </div>
</div>

<div style="text-align: center; margin-top: 60px;">
    <div class="signature-line" style="width: 400px; margin: 0 auto;">
        LocBem Equipamentos
    </div>
    <div style="margin-top: 10px;">
        CNPJ: 12.345.678/0001-90
    </div>
</div>

<div style="margin-top: 40px; text-align: center; font-size: 11px; color: #666;">
    <div>São Paulo/SP, {{ $recibo->data_emissao->format('d') }} de {{ $recibo->data_emissao->formatLocalized('%B') }} de {{ $recibo->data_emissao->format('Y') }}</div>
</div>

@php
function valorPorExtenso($valor) {
    $unidades = ['', 'um', 'dois', 'três', 'quatro', 'cinco', 'seis', 'sete', 'oito', 'nove'];
    $dezenas = ['', '', 'vinte', 'trinta', 'quarenta', 'cinquenta', 'sessenta', 'setenta', 'oitenta', 'noventa'];
    $especiais = ['dez', 'onze', 'doze', 'treze', 'quatorze', 'quinze', 'dezesseis', 'dezessete', 'dezoito', 'dezenove'];
    $centenas = ['', 'cento', 'duzentos', 'trezentos', 'quatrocentos', 'quinhentos', 'seiscentos', 'setecentos', 'oitocentos', 'novecentos'];
    
    $inteiro = intval($valor);
    $centavos = intval(($valor - $inteiro) * 100);
    
    if ($inteiro == 0) {
        $extenso = 'zero reais';
    } elseif ($inteiro == 1) {
        $extenso = 'um real';
    } else {
        // Simplificação para valores até 999
        if ($inteiro < 1000) {
            $c = intval($inteiro / 100);
            $d = intval(($inteiro % 100) / 10);
            $u = $inteiro % 10;
            
            $extenso = '';
            if ($c > 0) {
                if ($inteiro == 100) {
                    $extenso = 'cem';
                } else {
                    $extenso = $centenas[$c];
                }
            }
            
            if ($d == 1 && $u > 0) {
                $extenso .= ($extenso ? ' e ' : '') . $especiais[$u];
            } else {
                if ($d > 0) {
                    $extenso .= ($extenso ? ' e ' : '') . $dezenas[$d];
                }
                if ($u > 0) {
                    $extenso .= ($extenso ? ' e ' : '') . $unidades[$u];
                }
            }
            
            $extenso .= ' reais';
        } else {
            $extenso = number_format($inteiro, 0, ',', '.') . ' reais';
        }
    }
    
    if ($centavos > 0) {
        if ($centavos == 1) {
            $extenso .= ' e um centavo';
        } else {
            $extenso .= ' e ' . $centavos . ' centavos';
        }
    }
    
    return $extenso;
}
@endphp
@endsection
