import api from './api.js'

export const equipamentosService = {
  // Listar equipamentos com filtros e paginação
  async listar(params = {}) {
    try {
      const response = await api.get('/equipamentos', { params })
      return response.data
    } catch (error) {
      console.error('Erro ao listar equipamentos:', error)
      throw error
    }
  },

  // Buscar equipamento por ID
  async buscarPorId(id) {
    try {
      const response = await api.get(`/equipamentos/${id}`)
      return response.data
    } catch (error) {
      console.error('Erro ao buscar equipamento:', error)
      throw error
    }
  },

  // Criar novo equipamento
  async criar(equipamento) {
    try {
      const response = await api.post('/equipamentos', equipamento)
      return response.data
    } catch (error) {
      console.error('Erro ao criar equipamento:', error)
      throw error
    }
  },

  // Atualizar equipamento
  async atualizar(id, equipamento) {
    try {
      const response = await api.put(`/equipamentos/${id}`, equipamento)
      return response.data
    } catch (error) {
      console.error('Erro ao atualizar equipamento:', error)
      throw error
    }
  },

  // Excluir equipamento
  async excluir(id) {
    try {
      const response = await api.delete(`/equipamentos/${id}`)
      return response.data
    } catch (error) {
      console.error('Erro ao excluir equipamento:', error)
      throw error
    }
  }
}

export const categoriasService = {
  // Listar categorias
  async listar() {
    try {
      const response = await api.get('/categorias')
      return response.data
    } catch (error) {
      console.error('Erro ao listar categorias:', error)
      throw error
    }
  }
}
