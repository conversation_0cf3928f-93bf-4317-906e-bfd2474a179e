import api from './api.js'

export const relatoriosService = {
  // Dashboard executivo
  async dashboardExecutivo() {
    try {
      const response = await api.get('/relatorios/dashboard-executivo')
      return response.data
    } catch (error) {
      console.error('Erro ao carregar dashboard executivo:', error)
      throw error
    }
  },

  // Performance de equipamentos
  async performanceEquipamentos(params = {}) {
    try {
      const response = await api.get('/relatorios/performance-equipamentos', { params })
      return response.data
    } catch (error) {
      console.error('Erro ao carregar performance de equipamentos:', error)
      throw error
    }
  },

  // Análise financeira
  async analiseFinanceira(params = {}) {
    try {
      const response = await api.get('/relatorios/analise-financeira', { params })
      return response.data
    } catch (error) {
      console.error('Erro ao carregar análise financeira:', error)
      throw error
    }
  },

  // Relatório de clientes
  async relatorioClientes() {
    try {
      const response = await api.get('/relatorios/clientes')
      return response.data
    } catch (error) {
      console.error('Erro ao carregar relatório de clientes:', error)
      throw error
    }
  }
}
