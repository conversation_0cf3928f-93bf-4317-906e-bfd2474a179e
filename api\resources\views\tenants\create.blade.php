<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LocBem - Novo Tenant</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Novo Tenant</h1>
                        <p class="mt-2 text-gray-600">Criar um novo tenant no sistema</p>
                    </div>
                    <div>
                        <a href="{{ route('tenants.index') }}" 
                           class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            Voltar
                        </a>
                    </div>
                </div>
            </div>

            <!-- Form -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <form method="POST" action="{{ route('tenants.store') }}">
                    @csrf
                    
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Informações do Tenant</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500">Preencha os dados básicos do novo tenant</p>
                    </div>
                    
                    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                        <div class="grid grid-cols-1 gap-6">
                            <!-- Nome -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700">Nome *</label>
                                <input type="text" name="name" id="name" required
                                       value="{{ old('name') }}"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('name') border-red-300 @enderror"
                                       onkeyup="generateSlug()">
                                @error('name')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Slug -->
                            <div>
                                <label for="slug" class="block text-sm font-medium text-gray-700">Slug (ID do Tenant)</label>
                                <input type="text" name="slug" id="slug"
                                       value="{{ old('slug') }}"
                                       placeholder="sera-gerado-automaticamente"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('slug') border-red-300 @enderror">
                                <p class="mt-2 text-sm text-gray-500">Deixe em branco para gerar automaticamente a partir do nome. Use apenas letras, números e hífens.</p>
                                @error('slug')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700">Email *</label>
                                <input type="email" name="email" id="email" required
                                       value="{{ old('email') }}"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('email') border-red-300 @enderror">
                                @error('email')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Telefone -->
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700">Telefone</label>
                                <input type="text" name="phone" id="phone"
                                       value="{{ old('phone') }}"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('phone') border-red-300 @enderror">
                                @error('phone')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- CNPJ -->
                            <div>
                                <label for="cnpj" class="block text-sm font-medium text-gray-700">CNPJ</label>
                                <input type="text" name="cnpj" id="cnpj"
                                       value="{{ old('cnpj') }}"
                                       placeholder="00.000.000/0001-00"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('cnpj') border-red-300 @enderror">
                                @error('cnpj')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Endereço -->
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700">Endereço</label>
                                <textarea name="address" id="address" rows="3"
                                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('address') border-red-300 @enderror">{{ old('address') }}</textarea>
                                @error('address')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Cidade e Estado -->
                            <div class="grid grid-cols-1 gap-6 sm:grid-cols-3">
                                <div class="sm:col-span-2">
                                    <label for="city" class="block text-sm font-medium text-gray-700">Cidade</label>
                                    <input type="text" name="city" id="city"
                                           value="{{ old('city') }}"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('city') border-red-300 @enderror">
                                    @error('city')
                                        <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="state" class="block text-sm font-medium text-gray-700">Estado</label>
                                    <input type="text" name="state" id="state" maxlength="2"
                                           value="{{ old('state') }}"
                                           placeholder="SP"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('state') border-red-300 @enderror">
                                    @error('state')
                                        <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- CEP -->
                            <div>
                                <label for="zip_code" class="block text-sm font-medium text-gray-700">CEP</label>
                                <input type="text" name="zip_code" id="zip_code"
                                       value="{{ old('zip_code') }}"
                                       placeholder="00000-000"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('zip_code') border-red-300 @enderror">
                                @error('zip_code')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Domínio -->
                            <div>
                                <label for="domain" class="block text-sm font-medium text-gray-700">Domínio Principal *</label>
                                <input type="text" name="domain" id="domain" required
                                       value="{{ old('domain') }}"
                                       placeholder="exemplo.com"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('domain') border-red-300 @enderror">
                                <p class="mt-2 text-sm text-gray-500">Este será o domínio principal para acessar o tenant</p>
                                @error('domain')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Plano de Assinatura -->
                            <div>
                                <label for="subscription_plan" class="block text-sm font-medium text-gray-700">Plano de Assinatura</label>
                                <select name="subscription_plan" id="subscription_plan"
                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm @error('subscription_plan') border-red-300 @enderror">
                                    <option value="trial" {{ old('subscription_plan') == 'trial' ? 'selected' : '' }}>Trial (30 dias)</option>
                                    <option value="basic" {{ old('subscription_plan') == 'basic' ? 'selected' : '' }}>Básico</option>
                                    <option value="premium" {{ old('subscription_plan') == 'premium' ? 'selected' : '' }}>Premium</option>
                                    <option value="enterprise" {{ old('subscription_plan') == 'enterprise' ? 'selected' : '' }}>Enterprise</option>
                                </select>
                                @error('subscription_plan')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                        <button type="submit"
                                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Criar Tenant
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded shadow-lg">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded shadow-lg">
            {{ session('error') }}
        </div>
    @endif

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.fixed.top-4.right-4');
            alerts.forEach(alert => {
                alert.style.display = 'none';
            });
        }, 5000);

        // Generate slug from name
        function generateSlug() {
            const nameInput = document.getElementById('name');
            const slugInput = document.getElementById('slug');

            if (nameInput.value && !slugInput.dataset.userModified) {
                let slug = nameInput.value
                    .toLowerCase()
                    .normalize('NFD')
                    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
                    .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
                    .replace(/\s+/g, '-') // Substitui espaços por hífens
                    .replace(/-+/g, '-') // Remove hífens duplicados
                    .replace(/^-|-$/g, '') // Remove hífens no início e fim
                    .substring(0, 50); // Limita a 50 caracteres

                slugInput.value = slug;
                slugInput.placeholder = slug || 'sera-gerado-automaticamente';
            }
        }

        // Mark slug as user-modified when user types in it
        document.getElementById('slug').addEventListener('input', function() {
            this.dataset.userModified = 'true';
        });
    </script>
</body>
</html>
