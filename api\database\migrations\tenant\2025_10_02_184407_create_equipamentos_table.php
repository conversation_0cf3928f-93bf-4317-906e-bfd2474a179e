<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('equipamentos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('categoria_id')->constrained('categorias');
            $table->string('nome');
            $table->string('codigo_sku')->unique();
            $table->text('descricao')->nullable();
            $table->text('especificacoes_tecnicas')->nullable();
            $table->integer('quantidade_total')->default(0);
            $table->integer('quantidade_disponivel')->default(0);
            $table->integer('quantidade_locada')->default(0);
            $table->integer('quantidade_manutencao')->default(0);
            $table->decimal('valor_diaria', 10, 2);
            $table->enum('status', ['disponivel', 'manutencao', 'inativo'])->default('disponivel');
            $table->integer('estoque_minimo')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->index(['categoria_id', 'status']);
            $table->index('nome');
            $table->index('codigo_sku');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('equipamentos');
    }
};
