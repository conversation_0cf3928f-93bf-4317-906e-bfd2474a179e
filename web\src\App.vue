<template>
  <div id="app">
    <!-- Loading enquanto inicializa -->
    <AppLoading v-if="authStore.initializing" />

    <!-- Layout condicional baseado na rota -->
    <template v-else-if="$route.name === 'Login'">
      <router-view />
    </template>
    <template v-else>
      <AppLayout>
        <router-view />
      </AppLayout>
    </template>
  </div>
</template>

<script>
import { onMounted } from 'vue'
import { useAuthStore } from './stores/auth.js'
import AppLayout from './components/AppLayout.vue'
import AppLoading from './components/AppLoading.vue'

export default {
  name: 'App',
  components: {
    AppLayout,
    AppLoading
  },
  setup() {
    const authStore = useAuthStore()

    onMounted(async () => {
      // Inicializar store de autenticação
      await authStore.initialize()
    })

    return {
      authStore
    }
  }
}
</script>
