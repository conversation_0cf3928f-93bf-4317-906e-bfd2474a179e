<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
              <h1 class="text-xl font-bold text-indigo-600">LocBem</h1>
            </div>

            <!-- Navigation Links -->
            <div class="hidden sm:ml-6 sm:flex sm:space-x-1 relative">
              <!-- Sliding indicator -->
              <div
                ref="activeIndicator"
                class="absolute bottom-0 h-0.5 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full transition-all duration-300 ease-out"
                :style="indicatorStyle"
              ></div>

              <router-link
                ref="dashboardLink"
                to="/dashboard"
                class="nav-link group relative px-4 py-3 rounded-lg font-medium text-sm flex items-center space-x-2 transition-all duration-200"
                :class="isActiveRoute('/dashboard') ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-indigo-600 hover:bg-indigo-50'"
                @mouseenter="updateIndicator($event)"
                @click="updateIndicator($event)"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
                </svg>
                <span>Dashboard</span>
              </router-link>

              <router-link
                v-if="canViewClientes"
                ref="clientesLink"
                to="/clientes"
                class="nav-link group relative px-4 py-3 rounded-lg font-medium text-sm flex items-center space-x-2 transition-all duration-200"
                :class="isActiveRoute('/clientes') ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-indigo-600 hover:bg-indigo-50'"
                @mouseenter="updateIndicator($event)"
                @click="updateIndicator($event)"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                <span>Clientes</span>
              </router-link>

              <router-link
                v-if="canViewEquipamentos"
                ref="equipamentosLink"
                to="/equipamentos"
                class="nav-link group relative px-4 py-3 rounded-lg font-medium text-sm flex items-center space-x-2 transition-all duration-200"
                :class="isActiveRoute('/equipamentos') ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-indigo-600 hover:bg-indigo-50'"
                @mouseenter="updateIndicator($event)"
                @click="updateIndicator($event)"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <span>Equipamentos</span>
              </router-link>

              <router-link
                v-if="canViewContratos"
                ref="contratosLink"
                to="/contratos"
                class="nav-link group relative px-4 py-3 rounded-lg font-medium text-sm flex items-center space-x-2 transition-all duration-200"
                :class="isActiveRoute('/contratos') ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-indigo-600 hover:bg-indigo-50'"
                @mouseenter="updateIndicator($event)"
                @click="updateIndicator($event)"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span>Contratos</span>
              </router-link>

              <router-link
                v-if="canViewFinanceiro"
                ref="financeiroLink"
                to="/financeiro"
                class="nav-link group relative px-4 py-3 rounded-lg font-medium text-sm flex items-center space-x-2 transition-all duration-200"
                :class="isActiveRoute('/financeiro') ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-indigo-600 hover:bg-indigo-50'"
                @mouseenter="updateIndicator($event)"
                @click="updateIndicator($event)"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
                <span>Financeiro</span>
              </router-link>

              <router-link
                v-if="canViewRelatorios"
                ref="relatoriosLink"
                to="/relatorios"
                class="nav-link group relative px-4 py-3 rounded-lg font-medium text-sm flex items-center space-x-2 transition-all duration-200"
                :class="isActiveRoute('/relatorios') ? 'text-indigo-600 bg-indigo-50' : 'text-gray-600 hover:text-indigo-600 hover:bg-indigo-50'"
                @mouseenter="updateIndicator($event)"
                @click="updateIndicator($event)"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span>Relatórios</span>
              </router-link>
            </div>
          </div>
          
          <!-- User Menu -->
          <div class="hidden sm:ml-6 sm:flex sm:items-center">
            <div class="ml-3 relative">
              <div class="flex items-center space-x-4">
                <span class="text-sm text-gray-700">
                  Olá, {{ user?.name }}
                </span>
                <button
                  @click="handleLogout"
                  class="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <span class="sr-only">Sair</span>
                  <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          
          <!-- Mobile menu button -->
          <div class="-mr-2 flex items-center sm:hidden">
            <button
              @click="mobileMenuOpen = !mobileMenuOpen"
              class="bg-white inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
            >
              <span class="sr-only">Abrir menu principal</span>
              <svg class="block h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
      
      <!-- Mobile menu -->
      <div v-show="mobileMenuOpen" class="sm:hidden">
        <div class="pt-2 pb-3 space-y-1">
          <router-link
            to="/dashboard"
            class="flex items-center space-x-3 px-4 py-3 text-base font-medium transition-colors duration-200"
            :class="isActiveRoute('/dashboard') ? 'bg-indigo-50 border-indigo-500 text-indigo-700 border-l-4' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 border-l-4'"
            @click="mobileMenuOpen = false"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
            </svg>
            <span>Dashboard</span>
          </router-link>

          <router-link
            v-if="canViewClientes"
            to="/clientes"
            class="flex items-center space-x-3 px-4 py-3 text-base font-medium transition-colors duration-200"
            :class="isActiveRoute('/clientes') ? 'bg-indigo-50 border-indigo-500 text-indigo-700 border-l-4' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 border-l-4'"
            @click="mobileMenuOpen = false"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            <span>Clientes</span>
          </router-link>

          <router-link
            v-if="canViewEquipamentos"
            to="/equipamentos"
            class="flex items-center space-x-3 px-4 py-3 text-base font-medium transition-colors duration-200"
            :class="isActiveRoute('/equipamentos') ? 'bg-indigo-50 border-indigo-500 text-indigo-700 border-l-4' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 border-l-4'"
            @click="mobileMenuOpen = false"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            <span>Equipamentos</span>
          </router-link>

          <router-link
            v-if="canViewContratos"
            to="/contratos"
            class="flex items-center space-x-3 px-4 py-3 text-base font-medium transition-colors duration-200"
            :class="isActiveRoute('/contratos') ? 'bg-indigo-50 border-indigo-500 text-indigo-700 border-l-4' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 border-l-4'"
            @click="mobileMenuOpen = false"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <span>Contratos</span>
          </router-link>

          <router-link
            v-if="canViewFinanceiro"
            to="/financeiro"
            class="flex items-center space-x-3 px-4 py-3 text-base font-medium transition-colors duration-200"
            :class="isActiveRoute('/financeiro') ? 'bg-indigo-50 border-indigo-500 text-indigo-700 border-l-4' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 border-l-4'"
            @click="mobileMenuOpen = false"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
            <span>Financeiro</span>
          </router-link>

          <router-link
            v-if="canViewRelatorios"
            to="/relatorios"
            class="flex items-center space-x-3 px-4 py-3 text-base font-medium transition-colors duration-200"
            :class="isActiveRoute('/relatorios') ? 'bg-indigo-50 border-indigo-500 text-indigo-700 border-l-4' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 border-l-4'"
            @click="mobileMenuOpen = false"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <span>Relatórios</span>
          </router-link>
        </div>
        
        <div class="pt-4 pb-3 border-t border-gray-200">
          <div class="flex items-center px-4">
            <div class="ml-3">
              <div class="text-base font-medium text-gray-800">{{ user?.name }}</div>
              <div class="text-sm font-medium text-gray-500">{{ user?.email }}</div>
            </div>
          </div>
          <div class="mt-3 space-y-1">
            <button
              @click="handleLogout"
              class="block px-4 py-2 text-base font-medium text-gray-500 hover:text-gray-800 hover:bg-gray-100 w-full text-left"
            >
              Sair
            </button>
          </div>
        </div>
      </div>
    </nav>
    
    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <slot />
      </div>
    </main>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'

export default {
  name: 'AppLayout',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const authStore = useAuthStore()
    const mobileMenuOpen = ref(false)
    const activeIndicator = ref(null)
    const indicatorStyle = ref({
      width: '0px',
      left: '0px',
      opacity: '0'
    })

    const user = computed(() => authStore.user)

    // Computed properties para cada permissão para garantir reatividade
    const canViewClientes = computed(() => authStore.hasPermission('clientes.view'))
    const canViewEquipamentos = computed(() => authStore.hasPermission('equipamentos.view'))
    const canViewContratos = computed(() => authStore.hasPermission('contratos.view'))
    const canViewFinanceiro = computed(() => authStore.hasPermission('financeiro.view'))
    const canViewRelatorios = computed(() => authStore.hasPermission('relatorios.view'))

    // Função auxiliar para outras verificações de permissão
    const hasPermission = (permission) => {
      return authStore.hasPermission(permission)
    }

    // Função para verificar se uma rota está ativa
    const isActiveRoute = (path) => {
      return route.path === path || route.path.startsWith(path + '/')
    }

    // Função para atualizar a posição do indicador
    const updateIndicator = (event) => {
      const element = event.currentTarget
      const rect = element.getBoundingClientRect()
      const parentRect = element.parentElement.getBoundingClientRect()

      indicatorStyle.value = {
        width: `${rect.width}px`,
        left: `${rect.left - parentRect.left}px`,
        opacity: '1'
      }
    }

    // Função para posicionar o indicador no item ativo
    const positionActiveIndicator = async () => {
      await nextTick()

      // Encontrar o link ativo
      const activeLink = document.querySelector('.nav-link.router-link-active')
      if (activeLink) {
        const rect = activeLink.getBoundingClientRect()
        const parentRect = activeLink.parentElement.getBoundingClientRect()

        indicatorStyle.value = {
          width: `${rect.width}px`,
          left: `${rect.left - parentRect.left}px`,
          opacity: '1'
        }
      }
    }

    const handleLogout = async () => {
      await authStore.logout()
      router.push('/login')
    }

    // Posicionar o indicador quando o componente for montado
    onMounted(() => {
      positionActiveIndicator()
    })

    // Observar mudanças de rota para reposicionar o indicador
    router.afterEach(() => {
      nextTick(() => {
        positionActiveIndicator()
      })
    })

    return {
      user,
      mobileMenuOpen,
      activeIndicator,
      indicatorStyle,
      canViewClientes,
      canViewEquipamentos,
      canViewContratos,
      canViewFinanceiro,
      canViewRelatorios,
      hasPermission,
      isActiveRoute,
      updateIndicator,
      handleLogout
    }
  }
}
</script>
