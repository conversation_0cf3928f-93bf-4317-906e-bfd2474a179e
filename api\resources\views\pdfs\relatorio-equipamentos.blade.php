@extends('pdfs.layout')

@section('title', 'Relatório de Equipamentos')

@section('content')
<div class="document-title">
    RELATÓRIO DE EQUIPAMENTOS
</div>

<div class="document-info">
    <div class="left">
        <div class="info-group">
            <div class="info-label">Data do Relatório:</div>
            <div class="info-value">{{ now()->format('d/m/Y H:i') }}</div>
        </div>
        
        @if(isset($filtros['categoria_id']))
        <div class="info-group">
            <div class="info-label">Categoria Filtrada:</div>
            <div class="info-value">Sim</div>
        </div>
        @endif
        
        @if(isset($filtros['status']))
        <div class="info-group">
            <div class="info-label">Status Filtrado:</div>
            <div class="info-value">{{ ucfirst($filtros['status']) }}</div>
        </div>
        @endif
    </div>
    
    <div class="right">
        <div class="info-group">
            <div class="info-label">Total de Equipamentos:</div>
            <div class="info-value font-bold">{{ $resumo['total_equipamentos'] }}</div>
        </div>
        
        <div class="info-group">
            <div class="info-label">Valor Total Diário:</div>
            <div class="info-value font-bold text-blue">R$ {{ number_format($resumo['valor_total_diario'], 2, ',', '.') }}</div>
        </div>
    </div>
</div>

<div class="summary-box">
    <div class="summary-title">RESUMO POR STATUS</div>
    <div class="summary-item">
        <div class="summary-label">Equipamentos Disponíveis:</div>
        <div class="summary-value text-green">{{ $resumo['disponiveis'] }}</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">Equipamentos Locados:</div>
        <div class="summary-value text-blue">{{ $resumo['locados'] }}</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">Equipamentos em Manutenção:</div>
        <div class="summary-value text-yellow">{{ $resumo['manutencao'] }}</div>
    </div>
</div>

@if($equipamentos->count() > 0)
<div class="document-title" style="font-size: 16px; margin: 30px 0 20px;">
    DETALHAMENTO DOS EQUIPAMENTOS
</div>

<table class="table">
    <thead>
        <tr>
            <th>Equipamento</th>
            <th>Categoria</th>
            <th class="text-center">Status</th>
            <th class="text-center">Qtd Total</th>
            <th class="text-center">Qtd Locada</th>
            <th class="text-right">Valor Diário</th>
            <th class="text-right">Valor Mensal</th>
        </tr>
    </thead>
    <tbody>
        @foreach($equipamentos as $equipamento)
        <tr>
            <td>
                <strong>{{ $equipamento->nome }}</strong><br>
                <small>{{ $equipamento->codigo_sku }}</small>
                @if($equipamento->descricao)
                <br><small style="color: #666;">{{ Str::limit($equipamento->descricao, 40) }}</small>
                @endif
            </td>
            <td>{{ $equipamento->categoria->nome }}</td>
            <td class="text-center">
                <span class="
                    @if($equipamento->status === 'disponivel') text-green
                    @elseif($equipamento->status === 'locado') text-blue
                    @elseif($equipamento->status === 'manutencao') text-yellow
                    @else text-red
                    @endif
                    font-bold">
                    {{ ucfirst($equipamento->status) }}
                </span>
            </td>
            <td class="text-center">{{ $equipamento->quantidade_total }}</td>
            <td class="text-center">
                @if($equipamento->quantidade_locada > 0)
                    <span class="text-blue font-bold">{{ $equipamento->quantidade_locada }}</span>
                @else
                    0
                @endif
            </td>
            <td class="text-right">R$ {{ number_format($equipamento->valor_diario, 2, ',', '.') }}</td>
            <td class="text-right">R$ {{ number_format($equipamento->valor_mensal, 2, ',', '.') }}</td>
        </tr>
        @endforeach
    </tbody>
</table>

@if($equipamentos->count() > 15)
<div class="page-break"></div>
@endif

<!-- Resumo por Categoria -->
<div class="document-title" style="font-size: 16px; margin: 30px 0 20px;">
    RESUMO POR CATEGORIA
</div>

@php
    $categorias = $equipamentos->groupBy('categoria.nome');
@endphp

<table class="table">
    <thead>
        <tr>
            <th>Categoria</th>
            <th class="text-center">Qtd Equipamentos</th>
            <th class="text-center">Disponíveis</th>
            <th class="text-center">Locados</th>
            <th class="text-right">Valor Total Diário</th>
        </tr>
    </thead>
    <tbody>
        @foreach($categorias as $nomeCategoria => $equipamentosCategoria)
        @php
            $totalCategoria = $equipamentosCategoria->count();
            $disponiveisCategoria = $equipamentosCategoria->where('status', 'disponivel')->count();
            $locadosCategoria = $equipamentosCategoria->where('quantidade_locada', '>', 0)->count();
            $valorTotalCategoria = $equipamentosCategoria->sum('valor_diario');
        @endphp
        <tr>
            <td><strong>{{ $nomeCategoria }}</strong></td>
            <td class="text-center">{{ $totalCategoria }}</td>
            <td class="text-center text-green">{{ $disponiveisCategoria }}</td>
            <td class="text-center text-blue">{{ $locadosCategoria }}</td>
            <td class="text-right font-bold">R$ {{ number_format($valorTotalCategoria, 2, ',', '.') }}</td>
        </tr>
        @endforeach
    </tbody>
</table>

@else
<div class="summary-box" style="text-align: center; padding: 40px;">
    <div style="font-size: 16px; color: #666;">
        Nenhum equipamento encontrado com os filtros aplicados.
    </div>
</div>
@endif

<div class="summary-box" style="margin-top: 40px;">
    <div class="summary-title">ANÁLISE DE PERFORMANCE</div>
    <div style="font-size: 11px; line-height: 1.4; text-align: justify;">
        @php
            $taxaUtilizacao = $resumo['total_equipamentos'] > 0 ? ($resumo['locados'] / $resumo['total_equipamentos']) * 100 : 0;
            $taxaDisponibilidade = $resumo['total_equipamentos'] > 0 ? ($resumo['disponiveis'] / $resumo['total_equipamentos']) * 100 : 0;
        @endphp
        
        <strong>Taxa de Utilização:</strong> {{ number_format($taxaUtilizacao, 1) }}% dos equipamentos estão atualmente locados.
        @if($taxaUtilizacao > 80)
        <span class="text-green"> (Excelente utilização)</span>
        @elseif($taxaUtilizacao < 50)
        <span class="text-yellow"> (Baixa utilização - considere estratégias de marketing)</span>
        @endif
        <br><br>
        
        <strong>Taxa de Disponibilidade:</strong> {{ number_format($taxaDisponibilidade, 1) }}% dos equipamentos estão disponíveis para locação.
        @if($taxaDisponibilidade < 20)
        <span class="text-red"> (Estoque baixo - considere aquisição de novos equipamentos)</span>
        @elseif($taxaDisponibilidade > 70)
        <span class="text-yellow"> (Alto estoque disponível)</span>
        @endif
        <br><br>
        
        @if($resumo['manutencao'] > 0)
        <strong>Equipamentos em Manutenção:</strong> {{ $resumo['manutencao'] }} equipamento(s) necessitam atenção.
        <span class="text-yellow"> (Acompanhe o cronograma de manutenção)</span><br><br>
        @endif
        
        <strong>Potencial de Receita Diária:</strong> R$ {{ number_format($resumo['valor_total_diario'], 2, ',', '.') }} 
        (considerando todos os equipamentos locados simultaneamente).
    </div>
</div>

<div style="margin-top: 30px; text-align: right; font-size: 11px; color: #666;">
    <div>Relatório gerado automaticamente pelo sistema LocBem</div>
    <div>{{ now()->format('d/m/Y H:i:s') }}</div>
</div>
@endsection
