<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;

class TenantManagementController extends Controller
{
    /**
     * Display a listing of tenants.
     */
    public function index()
    {
        $tenants = Tenant::with('domains')->paginate(15);
        
        $stats = [
            'total_tenants' => Tenant::count(),
            'active_tenants' => Tenant::where('subscription_status', 'active')->count(),
            'trial_tenants' => Tenant::where('subscription_plan', 'trial')->count(),
            'suspended_tenants' => Tenant::where('subscription_status', 'suspended')->count(),
        ];
        
        return view('tenants.index', compact('tenants', 'stats'));
    }

    /**
     * Show the form for creating a new tenant.
     */
    public function create()
    {
        return view('tenants.create');
    }

    /**
     * Store a newly created tenant.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:tenants,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:2',
            'zip_code' => 'nullable|string|max:10',
            'cnpj' => 'nullable|string|max:18|unique:tenants,cnpj',
            'domain' => 'required|string|unique:domains,domain',
            'subscription_plan' => 'nullable|string|in:trial,basic,premium,enterprise',
            'slug' => 'nullable|string|max:50|unique:tenants,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Generate slug from name or use provided slug
            $slug = $request->slug ?: $this->generateSlug($request->name);

            // Ensure slug is unique
            $originalSlug = $slug;
            $counter = 1;
            while (Tenant::where('id', $slug)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Create tenant
            $tenant = Tenant::create([
                'id' => $slug,
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'zip_code' => $request->zip_code,
                'cnpj' => $request->cnpj,
                'subscription_plan' => $request->subscription_plan ?? 'trial',
                'subscription_status' => 'active',
                'trial_ends_at' => now()->addDays(30),
            ]);

            // Create domain
            $tenant->domains()->create([
                'domain' => $request->domain,
            ]);

            // Execute migrations automatically
            \Artisan::call('tenants:migrate', [
                '--tenants' => [$slug]
            ]);

            // Create default admin user
            $this->createDefaultAdminUser($tenant);

            return redirect()->route('tenants.index')
                ->with('success', 'Tenant criado com sucesso! Migrações executadas e usuário admin criado (<EMAIL> / 123456).');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erro ao criar tenant: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified tenant.
     */
    public function show(string $id)
    {
        $tenant = Tenant::with('domains')->findOrFail($id);
        
        // Get tenant statistics
        $stats = [];
        $tenant->run(function() use (&$stats) {
            try {
                $stats = [
                    'clientes_total' => \DB::table('clientes')->count(),
                    'equipamentos_total' => \DB::table('equipamentos')->count(),
                    'contratos_ativos' => \DB::table('contratos')->where('status', 'ativo')->count(),
                ];
            } catch (\Exception $e) {
                $stats = [
                    'clientes_total' => 0,
                    'equipamentos_total' => 0,
                    'contratos_ativos' => 0,
                ];
            }
        });
        
        return view('tenants.show', compact('tenant', 'stats'));
    }

    /**
     * Show the form for editing the specified tenant.
     */
    public function edit(string $id)
    {
        $tenant = Tenant::with('domains')->findOrFail($id);
        return view('tenants.edit', compact('tenant'));
    }

    /**
     * Update the specified tenant.
     */
    public function update(Request $request, string $id)
    {
        $tenant = Tenant::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:tenants,email,' . $id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:2',
            'zip_code' => 'nullable|string|max:10',
            'cnpj' => 'nullable|string|max:18|unique:tenants,cnpj,' . $id,
            'subscription_plan' => 'nullable|string|in:trial,basic,premium,enterprise',
            'subscription_status' => 'nullable|string|in:active,suspended,cancelled',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $tenant->update($request->only([
                'name', 'email', 'phone', 'address', 'city', 'state', 
                'zip_code', 'cnpj', 'subscription_plan', 'subscription_status'
            ]));

            return redirect()->route('tenants.index')
                ->with('success', 'Tenant atualizado com sucesso!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erro ao atualizar tenant: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified tenant.
     */
    public function destroy(string $id)
    {
        try {
            $tenant = Tenant::findOrFail($id);
            $tenant->delete();

            return redirect()->route('tenants.index')
                ->with('success', 'Tenant removido com sucesso!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erro ao remover tenant: ' . $e->getMessage());
        }
    }

    /**
     * Run tenant migrations.
     */
    public function migrate(string $id)
    {
        try {
            $tenant = Tenant::findOrFail($id);
            
            // Run migrations for this tenant
            \Artisan::call('tenants:migrate', [
                '--tenants' => [$id]
            ]);

            return redirect()->back()
                ->with('success', 'Migrações executadas com sucesso para o tenant!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erro ao executar migrações: ' . $e->getMessage());
        }
    }

    /**
     * Seed tenant database.
     */
    public function seed(string $id)
    {
        try {
            $tenant = Tenant::findOrFail($id);

            // Run seeders for this tenant
            \Artisan::call('tenants:seed', [
                '--tenants' => [$id]
            ]);

            return redirect()->back()
                ->with('success', 'Seeders executados com sucesso para o tenant!');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Erro ao executar seeders: ' . $e->getMessage());
        }
    }

    /**
     * Generate a slug from the tenant name.
     */
    private function generateSlug(string $name): string
    {
        // Remove acentos e caracteres especiais
        $slug = Str::slug($name, '-');

        // Limitar a 50 caracteres
        $slug = Str::limit($slug, 50, '');

        // Remove traços no final
        $slug = rtrim($slug, '-');

        return $slug;
    }

    /**
     * Create default admin user for the tenant.
     */
    private function createDefaultAdminUser(Tenant $tenant): void
    {
        $tenant->run(function() {
            try {
                // Create admin user
                $userId = \DB::table('users')->insertGetId([
                    'name' => 'Administrador',
                    'email' => '<EMAIL>',
                    'password' => bcrypt('123456'),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Create admin role if it doesn't exist
                $adminRoleId = \DB::table('roles')->where('name', 'admin')->value('id');
                if (!$adminRoleId) {
                    $adminRoleId = \DB::table('roles')->insertGetId([
                        'name' => 'admin',
                        'guard_name' => 'api',
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }

                // Assign admin role to user
                if ($userId && $adminRoleId) {
                    \DB::table('model_has_roles')->insert([
                        'role_id' => $adminRoleId,
                        'model_type' => 'App\\Models\\User',
                        'model_id' => $userId,
                    ]);
                }

            } catch (\Exception $e) {
                // Log error but don't fail the tenant creation
                \Log::error('Failed to create default admin user for tenant: ' . $e->getMessage());
            }
        });
    }
}
