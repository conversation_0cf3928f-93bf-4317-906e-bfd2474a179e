import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'

// Importar componentes
import Login from '../views/Login.vue'
import Dashboard from '../views/Dashboard.vue'
import Clientes from '../views/Clientes.vue'
import Equipamentos from '../views/Equipamentos.vue'
import Contratos from '../views/Contratos.vue'
import Financeiro from '../views/Financeiro.vue'
import Relatorios from '../views/Relatorios.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/clientes',
    name: 'Clientes',
    component: Clientes,
    meta: { 
      requiresAuth: true,
      permission: 'clientes.view'
    }
  },
  {
    path: '/equipamentos',
    name: 'Equipamentos',
    component: Equipamentos,
    meta: { 
      requiresAuth: true,
      permission: 'equipamentos.view'
    }
  },
  {
    path: '/contratos',
    name: 'Contra<PERSON>',
    component: Contratos,
    meta: {
      requiresAuth: true,
      permission: 'contratos.view'
    }
  },
  {
    path: '/financeiro',
    name: 'Financeiro',
    component: Financeiro,
    meta: {
      requiresAuth: true,
      permission: 'financeiro.view'
    }
  },
  {
    path: '/relatorios',
    name: 'Relatorios',
    component: Relatorios,
    meta: {
      requiresAuth: true,
      permission: 'relatorios.view'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Guard de autenticação
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Inicializar store se necessário e aguardar a inicialização
  if (!authStore.isAuthenticated && authStore.token === null) {
    await authStore.initialize()
  }

  // Verificar se a rota requer autenticação
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
    return
  }

  // Verificar se a rota é apenas para visitantes
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next('/dashboard')
    return
  }

  // Verificar permissões
  if (to.meta.permission && !authStore.hasPermission(to.meta.permission)) {
    // Redirecionar para dashboard se não tiver permissão
    next('/dashboard')
    return
  }

  next()
})

export default router
