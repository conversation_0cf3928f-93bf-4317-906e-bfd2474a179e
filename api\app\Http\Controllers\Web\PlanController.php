<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PlanController extends Controller
{
    /**
     * Display a listing of plans.
     */
    public function index()
    {
        $plans = [
            [
                'id' => 'trial',
                'name' => 'Trial',
                'description' => 'Plano de teste por 30 dias',
                'price' => 0,
                'duration_days' => 30,
                'features' => [
                    'Até 10 clientes',
                    'Até 20 equipamentos',
                    'Relatórios básicos',
                    'Suporte por email'
                ],
                'limits' => [
                    'clientes' => 10,
                    'equipamentos' => 20,
                    'contratos' => 5,
                    'usuarios' => 2
                ]
            ],
            [
                'id' => 'basic',
                'name' => 'Básico',
                'description' => 'Plano ideal para pequenas empresas',
                'price' => 49.90,
                'duration_days' => 30,
                'features' => [
                    'Até 50 clientes',
                    'Até 100 equipamentos',
                    'Relatórios completos',
                    'Suporte por email e chat',
                    'Backup automático'
                ],
                'limits' => [
                    'clientes' => 50,
                    'equipamentos' => 100,
                    'contratos' => 25,
                    'usuarios' => 5
                ]
            ],
            [
                'id' => 'premium',
                'name' => 'Premium',
                'description' => 'Plano para empresas em crescimento',
                'price' => 99.90,
                'duration_days' => 30,
                'features' => [
                    'Até 200 clientes',
                    'Até 500 equipamentos',
                    'Relatórios avançados',
                    'Suporte prioritário',
                    'Backup automático',
                    'API personalizada',
                    'Integrações'
                ],
                'limits' => [
                    'clientes' => 200,
                    'equipamentos' => 500,
                    'contratos' => 100,
                    'usuarios' => 15
                ]
            ],
            [
                'id' => 'enterprise',
                'name' => 'Enterprise',
                'description' => 'Plano para grandes empresas',
                'price' => 199.90,
                'duration_days' => 30,
                'features' => [
                    'Clientes ilimitados',
                    'Equipamentos ilimitados',
                    'Relatórios personalizados',
                    'Suporte 24/7',
                    'Backup em tempo real',
                    'API completa',
                    'Integrações avançadas',
                    'Gerente de conta dedicado'
                ],
                'limits' => [
                    'clientes' => -1, // -1 = ilimitado
                    'equipamentos' => -1,
                    'contratos' => -1,
                    'usuarios' => -1
                ]
            ]
        ];
        
        return view('plans.index', compact('plans'));
    }

    /**
     * Show the form for creating a new plan.
     */
    public function create()
    {
        return view('plans.create');
    }

    /**
     * Store a newly created plan.
     */
    public function store(Request $request)
    {
        // Para este exemplo, os planos são estáticos
        // Em uma implementação real, você salvaria no banco de dados
        return redirect()->route('plans.index')
            ->with('success', 'Plano criado com sucesso!');
    }

    /**
     * Display the specified plan.
     */
    public function show(string $id)
    {
        // Buscar plano por ID (implementação simplificada)
        return view('plans.show', compact('id'));
    }

    /**
     * Show the form for editing the specified plan.
     */
    public function edit(string $id)
    {
        return view('plans.edit', compact('id'));
    }

    /**
     * Update the specified plan.
     */
    public function update(Request $request, string $id)
    {
        // Para este exemplo, os planos são estáticos
        // Em uma implementação real, você atualizaria no banco de dados
        return redirect()->route('plans.index')
            ->with('success', 'Plano atualizado com sucesso!');
    }

    /**
     * Remove the specified plan.
     */
    public function destroy(string $id)
    {
        // Para este exemplo, os planos são estáticos
        // Em uma implementação real, você removeria do banco de dados
        return redirect()->route('plans.index')
            ->with('success', 'Plano removido com sucesso!');
    }
}
