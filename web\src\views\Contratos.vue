<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-2xl font-semibold text-gray-900">Contratos</h1>
        <p class="mt-2 text-sm text-gray-700">
          Lista de todos os contratos de locação.
        </p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <button
          v-if="hasPermission('contratos.create')"
          @click="novoContrato"
          type="button"
          class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Novo Contrato
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <div>
            <label for="search" class="block text-sm font-medium text-gray-700">Buscar</label>
            <input
              id="search"
              v-model="filters.buscar"
              type="text"
              placeholder="Número, cliente..."
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @input="debouncedSearch"
            />
          </div>

          <div>
            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
            <select
              id="status"
              v-model="filters.status"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @change="loadContratos"
            >
              <option value="">Todos</option>
              <option value="pendente">Pendente</option>
              <option value="ativo">Ativo</option>
              <option value="finalizado">Finalizado</option>
              <option value="cancelado">Cancelado</option>
            </select>
          </div>

          <div>
            <label for="data_inicio" class="block text-sm font-medium text-gray-700">Data Início</label>
            <input
              id="data_inicio"
              v-model="filters.data_inicio"
              type="date"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @change="loadContratos"
            />
          </div>

          <div>
            <label for="data_fim" class="block text-sm font-medium text-gray-700">Data Fim</label>
            <input
              id="data_fim"
              v-model="filters.data_fim"
              type="date"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @change="loadContratos"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div v-if="loading" class="p-6 text-center">
        <div class="inline-flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Carregando contratos...
        </div>
      </div>

      <div v-else-if="error" class="p-6 text-center text-red-600">
        {{ error }}
      </div>

      <div v-else-if="contratos.data && contratos.data.length === 0" class="p-6 text-center text-gray-500">
        Nenhum contrato encontrado.
      </div>

      <div v-else>
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contrato
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Cliente
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Período
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Valor
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="relative px-6 py-3">
                <span class="sr-only">Ações</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="contrato in contratos.data" :key="contrato.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    #{{ contrato.numero_contrato }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ contrato.itens?.length || 0 }} item(s)
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  {{ contrato.cliente?.nome_razao_social }}
                </div>
                <div class="text-sm text-gray-500">
                  {{ contrato.cliente?.cpf_cnpj }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div>{{ formatDate(contrato.data_inicio) }}</div>
                <div class="text-gray-500">até {{ formatDate(contrato.data_prevista_devolucao) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                R$ {{ formatCurrency(contrato.valor_final) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                      :class="getStatusClass(contrato.status)">
                  {{ getStatusLabel(contrato.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="verContrato(contrato)"
                    class="text-blue-600 hover:text-blue-900 p-1"
                    title="Ver detalhes"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                  </button>
                  <button
                    @click="gerarPdfContrato(contrato)"
                    class="text-red-600 hover:text-red-900 p-1"
                    title="Gerar PDF"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth.js'
import { contratosService } from '../services/contratos.js'
import { pdfService } from '../services/pdf.js'

export default {
  name: 'Contratos',
  setup() {
    const authStore = useAuthStore()
    const contratos = ref({})
    const loading = ref(false)
    const error = ref('')

    const filters = ref({
      buscar: '',
      status: '',
      data_inicio: '',
      data_fim: ''
    })

    const hasPermission = (permission) => {
      return authStore.hasPermission(permission)
    }

    const formatDate = (date) => {
      if (!date) return ''
      return new Date(date).toLocaleDateString('pt-BR')
    }

    const formatCurrency = (value) => {
      if (!value) return '0,00'
      return new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value)
    }

    const getStatusClass = (status) => {
      const classes = {
        'pendente': 'bg-yellow-100 text-yellow-800',
        'ativo': 'bg-green-100 text-green-800',
        'finalizado': 'bg-blue-100 text-blue-800',
        'cancelado': 'bg-red-100 text-red-800'
      }
      return classes[status] || 'bg-gray-100 text-gray-800'
    }

    const getStatusLabel = (status) => {
      const labels = {
        'pendente': 'Pendente',
        'ativo': 'Ativo',
        'finalizado': 'Finalizado',
        'cancelado': 'Cancelado'
      }
      return labels[status] || status
    }

    const loadContratos = async (page = 1) => {
      loading.value = true
      error.value = ''

      try {
        const params = {
          page,
          ...filters.value
        }

        // Remove empty filters
        Object.keys(params).forEach(key => {
          if (params[key] === '') {
            delete params[key]
          }
        })

        const response = await contratosService.listar(params)

        if (response.success) {
          contratos.value = response.data
        } else {
          error.value = response.message
        }
      } catch (err) {
        error.value = 'Erro ao carregar contratos'
        console.error('Erro ao carregar contratos:', err)
      } finally {
        loading.value = false
      }
    }

    const changePage = (page) => {
      loadContratos(page)
    }

    // Debounce search
    let searchTimeout
    const debouncedSearch = () => {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        loadContratos()
      }, 500)
    }

    const novoContrato = () => {
      // TODO: Implementar modal de novo contrato
      alert('Funcionalidade em desenvolvimento')
    }

    const verContrato = (contrato) => {
      // TODO: Implementar modal de detalhes
      alert(`Ver detalhes do contrato #${contrato.numero_contrato}`)
    }

    const editarContrato = (contrato) => {
      // TODO: Implementar modal de edição
      alert(`Editar contrato #${contrato.numero_contrato}`)
    }

    const finalizarContrato = async (contrato) => {
      if (!confirm(`Tem certeza que deseja finalizar o contrato #${contrato.numero_contrato}?`)) {
        return
      }

      try {
        const response = await contratosService.finalizar(contrato.id)
        if (response.success) {
          loadContratos()
        } else {
          error.value = response.message
        }
      } catch (err) {
        error.value = 'Erro ao finalizar contrato'
        console.error('Erro ao finalizar contrato:', err)
      }
    }

    const confirmarExclusao = async (contrato) => {
      if (!confirm(`Tem certeza que deseja excluir o contrato #${contrato.numero_contrato}?`)) {
        return
      }

      try {
        const response = await contratosService.excluir(contrato.id)
        if (response.success) {
          loadContratos()
        } else {
          error.value = response.message
        }
      } catch (err) {
        error.value = 'Erro ao excluir contrato'
        console.error('Erro ao excluir contrato:', err)
      }
    }

    const gerarPdfContrato = async (contrato) => {
      try {
        await pdfService.contrato(contrato.id)
      } catch (err) {
        alert('Erro ao gerar PDF do contrato')
        console.error('Erro ao gerar PDF:', err)
      }
    }

    onMounted(() => {
      loadContratos()
    })

    return {
      contratos,
      loading,
      error,
      filters,
      hasPermission,
      formatDate,
      formatCurrency,
      getStatusClass,
      getStatusLabel,
      loadContratos,
      changePage,
      debouncedSearch,
      novoContrato,
      verContrato,
      editarContrato,
      finalizarContrato,
      confirmarExclusao,
      gerarPdfContrato
    }
  }
}
</script>
