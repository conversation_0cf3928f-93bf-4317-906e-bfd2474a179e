<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-2xl font-semibold text-gray-900">Relatórios Avançados</h1>
        <p class="mt-2 text-sm text-gray-700">
          An<PERSON><PERSON><PERSON> detalhadas e insights do seu negócio
        </p>
      </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="[
            activeTab === tab.id
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
            'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
          ]"
        >
          {{ tab.name }}
        </button>
      </nav>
    </div>

    <!-- Dashboard Executivo -->
    <div v-if="activeTab === 'executivo'" class="space-y-6">
      <div v-if="loadingExecutivo" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-sm text-gray-500">Carregando dashboard executivo...</p>
      </div>
      
      <div v-else-if="dashboardExecutivo" class="space-y-6">
        <!-- Métricas Principais -->
        <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Receita do Mês</dt>
                    <dd class="text-lg font-medium text-gray-900">
                      R$ {{ formatCurrency(dashboardExecutivo.metricas.receita_mes_atual) }}
                    </dd>
                    <dd class="text-sm" :class="dashboardExecutivo.crescimento_receita >= 0 ? 'text-green-600' : 'text-red-600'">
                      {{ dashboardExecutivo.crescimento_receita >= 0 ? '+' : '' }}{{ dashboardExecutivo.crescimento_receita.toFixed(1) }}% vs mês anterior
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Contratos Ativos</dt>
                    <dd class="text-lg font-medium text-gray-900">{{ dashboardExecutivo.metricas.contratos_ativos }}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Equipamentos Locados</dt>
                    <dd class="text-lg font-medium text-gray-900">{{ dashboardExecutivo.metricas.equipamentos_locados }}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Contas Vencidas</dt>
                    <dd class="text-lg font-medium text-gray-900">{{ dashboardExecutivo.metricas.contas_vencidas }}</dd>
                    <dd class="text-sm text-red-600">
                      R$ {{ formatCurrency(dashboardExecutivo.metricas.valor_contas_vencidas) }}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Gráfico de Receita -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
              Evolução da Receita (Últimos 6 Meses)
            </h3>
            <div class="h-64 flex items-end justify-between space-x-2">
              <div
                v-for="(item, index) in dashboardExecutivo.receita_por_mes"
                :key="index"
                class="flex flex-col items-center flex-1"
              >
                <div
                  class="bg-blue-500 w-full rounded-t"
                  :style="{ height: getBarHeight(item.valor, dashboardExecutivo.receita_por_mes) + 'px' }"
                ></div>
                <div class="text-xs text-gray-500 mt-2">{{ item.mes }}</div>
                <div class="text-xs font-medium text-gray-900">
                  R$ {{ formatCurrency(item.valor) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Top Clientes e Equipamentos -->
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <!-- Top Clientes -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                Top 5 Clientes por Receita
              </h3>
              <div class="space-y-3">
                <div
                  v-for="(cliente, index) in dashboardExecutivo.top_clientes"
                  :key="cliente.id"
                  class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div class="flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {{ index + 1 }}
                    </div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-900">{{ cliente.nome_razao_social }}</p>
                      <p class="text-xs text-gray-500">{{ cliente.email }}</p>
                    </div>
                  </div>
                  <div class="text-sm font-medium text-gray-900">
                    R$ {{ formatCurrency(cliente.total_receita) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Equipamentos Mais Locados -->
          <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                Equipamentos Mais Locados
              </h3>
              <div class="space-y-3">
                <div
                  v-for="(equipamento, index) in dashboardExecutivo.equipamentos_mais_locados"
                  :key="equipamento.id"
                  class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div class="flex items-center">
                    <div class="flex-shrink-0 w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {{ index + 1 }}
                    </div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-900">{{ equipamento.nome }}</p>
                      <p class="text-xs text-gray-500">{{ equipamento.categoria_nome }}</p>
                    </div>
                  </div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ equipamento.total_locacoes }} locações
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance de Equipamentos -->
    <div v-if="activeTab === 'equipamentos'" class="space-y-6">
      <!-- Filtros -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label for="data_inicio_eq" class="block text-sm font-medium text-gray-700">Data Início</label>
              <input
                id="data_inicio_eq"
                type="date"
                v-model="filtrosEquipamentos.data_inicio"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
            </div>
            <div>
              <label for="data_fim_eq" class="block text-sm font-medium text-gray-700">Data Fim</label>
              <input
                id="data_fim_eq"
                type="date"
                v-model="filtrosEquipamentos.data_fim"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              >
            </div>
          </div>
          <div class="mt-4">
            <button
              @click="loadPerformanceEquipamentos"
              type="button"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Atualizar Relatório
            </button>
          </div>
        </div>
      </div>

      <div v-if="loadingEquipamentos" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-sm text-gray-500">Carregando performance de equipamentos...</p>
      </div>

      <div v-else-if="performanceEquipamentos" class="space-y-6">
        <!-- Placeholder para conteúdo de equipamentos -->
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Performance de Equipamentos</h3>
          <p class="text-gray-500">Dados carregados com sucesso. Interface em desenvolvimento...</p>
        </div>
      </div>
    </div>

    <!-- Análise Financeira -->
    <div v-if="activeTab === 'financeiro'" class="space-y-6">
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Análise Financeira</h3>
        <p class="text-gray-500">Interface em desenvolvimento...</p>
      </div>
    </div>

    <!-- Relatório de Clientes -->
    <div v-if="activeTab === 'clientes'" class="space-y-6">
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Relatório de Clientes</h3>
        <p class="text-gray-500">Interface em desenvolvimento...</p>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth.js'
import { relatoriosService } from '../services/relatorios.js'

export default {
  name: 'Relatorios',
  setup() {
    const authStore = useAuthStore()
    const activeTab = ref('executivo')

    // Dashboard Executivo
    const dashboardExecutivo = ref(null)
    const loadingExecutivo = ref(false)

    // Performance Equipamentos
    const performanceEquipamentos = ref(null)
    const loadingEquipamentos = ref(false)
    const filtrosEquipamentos = ref({
      data_inicio: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 3 meses atrás
      data_fim: new Date().toISOString().split('T')[0] // hoje
    })

    // Análise Financeira
    const analiseFinanceira = ref(null)
    const loadingFinanceiro = ref(false)

    // Relatório Clientes
    const relatorioClientes = ref(null)
    const loadingClientes = ref(false)

    const tabs = [
      { id: 'executivo', name: 'Dashboard Executivo' },
      { id: 'equipamentos', name: 'Performance Equipamentos' },
      { id: 'financeiro', name: 'Análise Financeira' },
      { id: 'clientes', name: 'Relatório Clientes' }
    ]

    const hasPermission = (permission) => {
      return authStore.hasPermission(permission)
    }

    const formatCurrency = (value) => {
      if (!value) return '0,00'
      return parseFloat(value).toLocaleString('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }

    const getBarHeight = (value, data) => {
      const maxValue = Math.max(...data.map(item => item.valor))
      if (maxValue === 0) return 20
      return Math.max(20, (value / maxValue) * 200)
    }

    const loadDashboardExecutivo = async () => {
      loadingExecutivo.value = true
      try {
        const response = await relatoriosService.dashboardExecutivo()
        if (response.success) {
          dashboardExecutivo.value = response.data
        }
      } catch (error) {
        console.error('Erro ao carregar dashboard executivo:', error)
      } finally {
        loadingExecutivo.value = false
      }
    }

    const loadPerformanceEquipamentos = async () => {
      loadingEquipamentos.value = true
      try {
        const response = await relatoriosService.performanceEquipamentos(filtrosEquipamentos.value)
        if (response.success) {
          performanceEquipamentos.value = response.data
        }
      } catch (error) {
        console.error('Erro ao carregar performance de equipamentos:', error)
      } finally {
        loadingEquipamentos.value = false
      }
    }

    const loadAnaliseFinanceira = async () => {
      loadingFinanceiro.value = true
      try {
        const response = await relatoriosService.analiseFinanceira()
        if (response.success) {
          analiseFinanceira.value = response.data
        }
      } catch (error) {
        console.error('Erro ao carregar análise financeira:', error)
      } finally {
        loadingFinanceiro.value = false
      }
    }

    const loadRelatorioClientes = async () => {
      loadingClientes.value = true
      try {
        const response = await relatoriosService.relatorioClientes()
        if (response.success) {
          relatorioClientes.value = response.data
        }
      } catch (error) {
        console.error('Erro ao carregar relatório de clientes:', error)
      } finally {
        loadingClientes.value = false
      }
    }

    onMounted(() => {
      loadDashboardExecutivo()
    })

    return {
      activeTab,
      tabs,
      dashboardExecutivo,
      loadingExecutivo,
      performanceEquipamentos,
      loadingEquipamentos,
      filtrosEquipamentos,
      analiseFinanceira,
      loadingFinanceiro,
      relatorioClientes,
      loadingClientes,
      hasPermission,
      formatCurrency,
      getBarHeight,
      loadDashboardExecutivo,
      loadPerformanceEquipamentos,
      loadAnaliseFinanceira,
      loadRelatorioClientes
    }
  }
}
</script>
