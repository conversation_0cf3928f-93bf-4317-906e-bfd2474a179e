# LocBem - Sistema de Gestão de Locação de Equipamentos

Sistema completo de gestão para lojas de locação de equipamentos de construção civil, com arquitetura moderna, multi-tenancy e alta escalabilidade.

## 🏗️ Arquitetura

### Stack Tecnológica
- **Frontend**: Vue.js 3 (Composition API) sem TypeScript
- **Backend**: Laravel 12 (API REST)
- **Banco de Dados**: PostgreSQL 18
- **Estrutura**: Monorepo com diretórios separados

```
/web - Aplicação Vue.js
/api - Aplicação Laravel
```

## 🚀 Funcionalidades

### ✅ Implementadas
- **Autenticação JWT** com roles e permissões
- **Multi-tenancy** com isolamento de dados
- **Gestão de Clientes** (CRUD completo)
- **Gestão de Equipamentos** (CRUD completo)
- **Dashboard** com estatísticas
- **Interface responsiva** com TailwindCSS
- **API REST** completa e documentada

### 🔄 Em Desenvolvimento
- **Gestão de Contratos** de locação
- **Sistema Financeiro** (contas a receber/pagar)
- **Geração de PDFs** (contratos, recibos, solicitações)
- **Gestão de Endereços** de entrega
- **Relatórios** avançados

## 📋 Pré-requisitos

- PHP 8.2+
- Composer
- Node.js 18+
- PostgreSQL 18+
- Extensão PHP: pdo_pgsql

## 🛠️ Instalação

### 1. Clone o repositório
```bash
git clone <repository-url>
cd locbem-new
```

### 2. Configuração do Backend (Laravel)

```bash
cd api

# Instalar dependências
composer install

# Configurar ambiente
cp .env.example .env

# Editar .env com suas configurações de banco
DB_CONNECTION=pgsql
DB_HOST=localhost
DB_PORT=5432
DB_DATABASE=locbem
DB_USERNAME=postgres
DB_PASSWORD="LocBem@2025!"

# Gerar chave da aplicação
php artisan key:generate

# Gerar chave JWT
php artisan jwt:secret

# Executar migrations e seeders
php artisan migrate:fresh --seed

# Iniciar servidor
php artisan serve --host=127.0.0.1 --port=8000
```

### 3. Configuração do Frontend (Vue.js)

```bash
cd web

# Instalar dependências
npm install

# Iniciar servidor de desenvolvimento
npm run dev
```

## 👥 Usuários Padrão

O sistema vem com usuários pré-configurados para teste:

| Email | Senha | Role |
|-------|-------|------|
| <EMAIL> | 123456 | Administrador |
| <EMAIL> | 123456 | Operador |
| <EMAIL> | 123456 | Financeiro |

## 🔗 URLs de Acesso

- **Frontend**: http://localhost:5174/
- **Backend API**: http://127.0.0.1:8000/api/

## 📚 Endpoints da API

### Autenticação
- `POST /api/auth/login` - Login
- `GET /api/auth/me` - Dados do usuário
- `POST /api/auth/logout` - Logout
- `POST /api/auth/refresh` - Refresh token

### Clientes
- `GET /api/clientes` - Listar clientes
- `POST /api/clientes` - Criar cliente
- `GET /api/clientes/{id}` - Visualizar cliente
- `PUT /api/clientes/{id}` - Atualizar cliente
- `DELETE /api/clientes/{id}` - Excluir cliente

### Equipamentos
- `GET /api/equipamentos` - Listar equipamentos
- `POST /api/equipamentos` - Criar equipamento
- `GET /api/equipamentos/{id}` - Visualizar equipamento
- `PUT /api/equipamentos/{id}` - Atualizar equipamento
- `DELETE /api/equipamentos/{id}` - Excluir equipamento

### Dashboard
- `GET /api/dashboard` - Estatísticas do dashboard

## 🗄️ Estrutura do Banco de Dados

### Principais Tabelas
- `tenants` - Multi-tenancy
- `users` - Usuários do sistema
- `clientes` - Clientes da locadora
- `cliente_enderecos` - Endereços de entrega
- `categorias` - Categorias de equipamentos
- `equipamentos` - Equipamentos disponíveis
- `contratos` - Contratos de locação
- `contrato_itens` - Itens dos contratos
- `periodos_locacao` - Períodos de locação
- `contas_receber` - Contas a receber
- `contas_pagar` - Contas a pagar

## 🔐 Sistema de Permissões

### Roles Disponíveis
- **admin**: Acesso total ao sistema
- **operador**: Gestão de clientes, equipamentos e contratos
- **financeiro**: Acesso ao módulo financeiro e relatórios

### Permissões por Módulo
- `clientes.*` - Gestão de clientes
- `equipamentos.*` - Gestão de equipamentos
- `contratos.*` - Gestão de contratos
- `financeiro.*` - Módulo financeiro

## 🎨 Interface

O sistema possui uma interface moderna e responsiva construída com:
- **Vue.js 3** com Composition API
- **TailwindCSS** para estilização
- **Vue Router** para navegação
- **Pinia** para gerenciamento de estado
- **Axios** para comunicação com API

## 🔧 Desenvolvimento

### Comandos Úteis

**Backend:**
```bash
# Limpar cache
php artisan config:clear
php artisan cache:clear

# Executar migrations
php artisan migrate

# Executar seeders
php artisan db:seed

# Listar rotas
php artisan route:list
```

**Frontend:**
```bash
# Build para produção
npm run build

# Preview da build
npm run preview
```

## 📝 Próximos Passos

1. **Implementar módulo de Contratos**
   - CRUD de contratos
   - Gestão de itens
   - Controle de períodos

2. **Sistema Financeiro**
   - Contas a receber/pagar
   - Relatórios financeiros
   - Dashboard financeiro

3. **Geração de PDFs**
   - Contratos
   - Recibos
   - Solicitações

4. **Melhorias na Interface**
   - Formulários modais
   - Validações em tempo real
   - Notificações toast

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo LICENSE para mais detalhes.

---

**Desenvolvido com ❤️ para facilitar a gestão de locação de equipamentos**
