<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Web\TenantManagementController;
use App\Http\Controllers\Web\PlanController;

Route::get('/', function () {
    return view('welcome');
});

// Rotas de administração
Route::prefix('admin')->group(function () {
    // Gerenciamento de tenants
    Route::get('/tenants', [TenantManagementController::class, 'index'])->name('tenants.index');
    Route::get('/tenants/create', [TenantManagementController::class, 'create'])->name('tenants.create');
    Route::post('/tenants', [TenantManagementController::class, 'store'])->name('tenants.store');
    Route::get('/tenants/{id}', [TenantManagementController::class, 'show'])->name('tenants.show');
    Route::get('/tenants/{id}/edit', [TenantManagementController::class, 'edit'])->name('tenants.edit');
    Route::put('/tenants/{id}', [TenantManagementController::class, 'update'])->name('tenants.update');
    Route::delete('/tenants/{id}', [TenantManagementController::class, 'destroy'])->name('tenants.destroy');
    Route::post('/tenants/{id}/migrate', [TenantManagementController::class, 'migrate'])->name('tenants.migrate');
    Route::post('/tenants/{id}/seed', [TenantManagementController::class, 'seed'])->name('tenants.seed');

    // Gerenciamento de planos
    Route::get('/plans', [PlanController::class, 'index'])->name('plans.index');
    Route::get('/plans/create', [PlanController::class, 'create'])->name('plans.create');
    Route::post('/plans', [PlanController::class, 'store'])->name('plans.store');
    Route::get('/plans/{id}', [PlanController::class, 'show'])->name('plans.show');
    Route::get('/plans/{id}/edit', [PlanController::class, 'edit'])->name('plans.edit');
    Route::put('/plans/{id}', [PlanController::class, 'update'])->name('plans.update');
    Route::delete('/plans/{id}', [PlanController::class, 'destroy'])->name('plans.destroy');
});
