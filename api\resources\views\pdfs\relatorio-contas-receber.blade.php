@extends('pdfs.layout')

@section('title', 'Relatório de Contas a Receber')

@section('content')
<div class="document-title">
    RELATÓRIO DE CONTAS A RECEBER
</div>

<div class="document-info">
    <div class="left">
        <div class="info-group">
            <div class="info-label">Data do Relatório:</div>
            <div class="info-value">{{ now()->format('d/m/Y H:i') }}</div>
        </div>
        
        @if(isset($filtros['data_inicio']) && isset($filtros['data_fim']))
        <div class="info-group">
            <div class="info-label">Período:</div>
            <div class="info-value">
                {{ \Carbon\Carbon::parse($filtros['data_inicio'])->format('d/m/Y') }} até 
                {{ \Carbon\Carbon::parse($filtros['data_fim'])->format('d/m/Y') }}
            </div>
        </div>
        @endif
        
        @if(isset($filtros['status']))
        <div class="info-group">
            <div class="info-label">Status:</div>
            <div class="info-value">{{ ucfirst($filtros['status']) }}</div>
        </div>
        @endif
    </div>
    
    <div class="right">
        <div class="info-group">
            <div class="info-label">Total de Contas:</div>
            <div class="info-value font-bold">{{ $resumo['total_contas'] }}</div>
        </div>
        
        <div class="info-group">
            <div class="info-label">Valor Total:</div>
            <div class="info-value font-bold text-blue">R$ {{ number_format($resumo['valor_total'], 2, ',', '.') }}</div>
        </div>
    </div>
</div>

<div class="summary-box">
    <div class="summary-title">RESUMO GERAL</div>
    <div class="summary-item">
        <div class="summary-label">Contas Pendentes:</div>
        <div class="summary-value">{{ $resumo['pendentes'] }} (R$ {{ number_format($resumo['valor_pendente'], 2, ',', '.') }})</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">Contas Pagas:</div>
        <div class="summary-value text-green">{{ $resumo['pagas'] }} (R$ {{ number_format($resumo['valor_pago'], 2, ',', '.') }})</div>
    </div>
    <div class="summary-item">
        <div class="summary-label">Contas Vencidas:</div>
        <div class="summary-value text-red">{{ $resumo['vencidas'] }} (R$ {{ number_format($resumo['valor_vencido'], 2, ',', '.') }})</div>
    </div>
</div>

@if($contas->count() > 0)
<div class="document-title" style="font-size: 16px; margin: 30px 0 20px;">
    DETALHAMENTO DAS CONTAS
</div>

<table class="table">
    <thead>
        <tr>
            <th>Cliente</th>
            <th>Descrição</th>
            <th class="text-center">Vencimento</th>
            <th class="text-center">Status</th>
            <th class="text-right">Valor</th>
            <th class="text-center">Pagamento</th>
        </tr>
    </thead>
    <tbody>
        @foreach($contas as $conta)
        <tr>
            <td>
                <strong>{{ $conta->cliente->nome_razao_social ?? 'N/A' }}</strong>
                @if($conta->contrato)
                <br><small>Contrato: #{{ $conta->contrato->numero_contrato }}</small>
                @endif
            </td>
            <td>
                {{ Str::limit($conta->descricao, 40) }}
                @if($conta->observacoes)
                <br><small><em>{{ Str::limit($conta->observacoes, 30) }}</em></small>
                @endif
            </td>
            <td class="text-center">
                {{ $conta->data_vencimento->format('d/m/Y') }}
                @if($conta->data_vencimento->isPast() && $conta->status !== 'pago')
                <br><small class="text-red">({{ $conta->data_vencimento->diffInDays(now()) }} dias)</small>
                @endif
            </td>
            <td class="text-center">
                <span class="
                    @if($conta->status === 'pendente') text-yellow
                    @elseif($conta->status === 'pago') text-green
                    @elseif($conta->status === 'vencido') text-red
                    @endif
                    font-bold">
                    {{ ucfirst($conta->status) }}
                </span>
            </td>
            <td class="text-right font-bold">
                R$ {{ number_format($conta->valor, 2, ',', '.') }}
            </td>
            <td class="text-center">
                @if($conta->data_pagamento)
                    {{ $conta->data_pagamento->format('d/m/Y') }}
                    @if($conta->forma_pagamento)
                    <br><small>{{ $conta->forma_pagamento }}</small>
                    @endif
                @else
                    -
                @endif
            </td>
        </tr>
        @endforeach
    </tbody>
</table>

@if($contas->count() > 20)
<div class="page-break"></div>
@endif

@else
<div class="summary-box" style="text-align: center; padding: 40px;">
    <div style="font-size: 16px; color: #666;">
        Nenhuma conta a receber encontrada com os filtros aplicados.
    </div>
</div>
@endif

<div class="summary-box" style="margin-top: 40px;">
    <div class="summary-title">ANÁLISE FINANCEIRA</div>
    <div style="font-size: 11px; line-height: 1.4; text-align: justify;">
        @if($resumo['vencidas'] > 0)
        <strong>Atenção:</strong> Existem {{ $resumo['vencidas'] }} conta(s) vencida(s) totalizando R$ {{ number_format($resumo['valor_vencido'], 2, ',', '.') }}. 
        Recomenda-se contato imediato com os clientes para regularização.<br><br>
        @endif
        
        @if($resumo['pendentes'] > 0)
        <strong>Contas Pendentes:</strong> {{ $resumo['pendentes'] }} conta(s) no valor de R$ {{ number_format($resumo['valor_pendente'], 2, ',', '.') }} 
        aguardando pagamento.<br><br>
        @endif
        
        <strong>Taxa de Recebimento:</strong> 
        @php
            $taxaRecebimento = $resumo['total_contas'] > 0 ? ($resumo['pagas'] / $resumo['total_contas']) * 100 : 0;
        @endphp
        {{ number_format($taxaRecebimento, 1) }}% das contas foram pagas.
        
        @if($taxaRecebimento < 80)
        <span class="text-red"> (Atenção: Taxa abaixo do ideal)</span>
        @elseif($taxaRecebimento >= 95)
        <span class="text-green"> (Excelente performance)</span>
        @endif
    </div>
</div>

<div style="margin-top: 30px; text-align: right; font-size: 11px; color: #666;">
    <div>Relatório gerado automaticamente pelo sistema LocBem</div>
    <div>{{ now()->format('d/m/Y H:i:s') }}</div>
</div>
@endsection
