<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 to-blue-700 py-12 px-4">
    <div class="max-w-md w-full bg-white rounded-xl shadow-2xl p-8">
      <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-2">
          LocBem - Sistema de Locação
        </h2>
        <p class="text-gray-600 text-sm">
          Faça login para acessar o sistema
        </p>
      </div>
      
      <form class="space-y-6" @submit.prevent="handleLogin">
        <div class="space-y-4">
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
            <input
              id="email"
              v-model="form.email"
              name="email"
              type="email"
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="Digite seu email"
            />
          </div>
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Senha</label>
            <input
              id="password"
              v-model="form.password"
              name="password"
              type="password"
              required
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="Digite sua senha"
            />
          </div>
        </div>

        <div v-if="error" class="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
          {{ error }}
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading"
            class="w-full bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-medium py-3 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            <span v-if="loading">Entrando...</span>
            <span v-else>Entrar</span>
          </button>
        </div>

        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center text-xs text-gray-600">
          <p class="font-medium mb-2">Usuários de teste:</p>
          <div class="space-y-1">
            <p><span class="font-medium text-gray-800">Admin:</span> <EMAIL> / 123456</p>
            <p><span class="font-medium text-gray-800">Operador:</span> <EMAIL> / 123456</p>
            <p><span class="font-medium text-gray-800">Financeiro:</span> <EMAIL> / 123456</p>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    
    const form = ref({
      email: '',
      password: ''
    })
    
    const error = ref('')
    const loading = ref(false)
    
    const handleLogin = async () => {
      error.value = ''
      loading.value = true
      
      try {
        const result = await authStore.login(form.value)
        
        if (result.success) {
          router.push('/dashboard')
        } else {
          error.value = result.message
        }
      } catch (err) {
        error.value = 'Erro interno do sistema'
        console.error('Erro no login:', err)
      } finally {
        loading.value = false
      }
    }
    
    return {
      form,
      error,
      loading,
      handleLogin
    }
  }
}
</script>
