import api from './api.js'

export const authService = {
  // Login
  async login(credentials) {
    try {
      const response = await api.post('/auth/login', credentials)
      
      if (response.data.success) {
        const { token, user } = response.data.data
        
        // Salvar token e dados do usuário
        localStorage.setItem('auth_token', token)
        localStorage.setItem('user_data', JSON.stringify(user))
        
        return { success: true, data: response.data.data }
      }
      
      return { success: false, message: response.data.message }
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || 'Erro ao fazer login'
      }
    }
  },

  // Logout
  async logout() {
    try {
      await api.post('/auth/logout')
    } catch (error) {
      console.error('Erro ao fazer logout:', error)
    } finally {
      // Limpar dados locais independente do resultado da API
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_data')
    }
  },

  // Verificar se está autenticado
  isAuthenticated() {
    return !!localStorage.getItem('auth_token')
  },

  // Obter dados do usuário
  getUser() {
    const userData = localStorage.getItem('user_data')
    return userData ? JSON.parse(userData) : null
  },

  // Obter token
  getToken() {
    return localStorage.getItem('auth_token')
  },

  // Verificar dados do usuário atual
  async me() {
    try {
      const response = await api.get('/auth/me')
      
      if (response.data.success) {
        const user = response.data.data.user
        localStorage.setItem('user_data', JSON.stringify(user))
        return { success: true, data: user }
      }
      
      return { success: false, message: response.data.message }
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || 'Erro ao obter dados do usuário'
      }
    }
  }
}
