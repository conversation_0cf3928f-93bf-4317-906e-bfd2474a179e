<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('periodos_locacao', function (Blueprint $table) {
            $table->id();
            $table->string('nome', 100);
            $table->text('descricao')->nullable();
            $table->integer('dias_minimo');
            $table->integer('dias_maximo')->nullable();
            $table->decimal('multiplicador_preco', 8, 4)->default(1.0000);
            $table->decimal('desconto_percentual', 5, 2)->default(0);
            $table->boolean('is_ativo')->default(true);
            $table->integer('ordem')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->index(['is_ativo', 'ordem']);
            $table->index(['dias_minimo', 'dias_maximo']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('periodos_locacao');
    }
};
