<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LocBem - Gerenciamento de Tenants</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">LocBem - Gerenciamento de Tenants</h1>
                <p class="mt-2 text-gray-600">Gerencie todos os tenants do sistema</p>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold"><?php echo e($stats['total_tenants']); ?></span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total de Tenants</dt>
                                    <dd class="text-lg font-medium text-gray-900"><?php echo e($stats['total_tenants']); ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold"><?php echo e($stats['active_tenants']); ?></span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Ativos</dt>
                                    <dd class="text-lg font-medium text-gray-900"><?php echo e($stats['active_tenants']); ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold"><?php echo e($stats['trial_tenants']); ?></span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Em Trial</dt>
                                    <dd class="text-lg font-medium text-gray-900"><?php echo e($stats['trial_tenants']); ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold"><?php echo e($stats['suspended_tenants']); ?></span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Suspensos</dt>
                                    <dd class="text-lg font-medium text-gray-900"><?php echo e($stats['suspended_tenants']); ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="mb-6 flex justify-between items-center">
                <div class="flex space-x-4">
                    <a href="<?php echo e(route('plans.index')); ?>"
                       class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded inline-block">
                        Gerenciar Planos
                    </a>
                    <a href="<?php echo e(route('tenants.create')); ?>"
                       class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded inline-block">
                        Novo Tenant
                    </a>
                </div>
                <div>
                    <input 
                        type="text" 
                        placeholder="Buscar tenants..." 
                        class="border border-gray-300 rounded-md px-3 py-2 w-64"
                        x-model="searchTerm"
                    >
                </div>
            </div>

            <!-- Tenants Table -->
            <div class="bg-white shadow overflow-hidden sm:rounded-md" x-data="tenantsManager()">
                <ul class="divide-y divide-gray-200">
                    <?php $__currentLoopData = $tenants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tenant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="px-6 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                        <span class="text-gray-700 font-bold"><?php echo e(substr($tenant->name, 0, 2)); ?></span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($tenant->name); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($tenant->email); ?></div>
                                    <div class="text-xs text-gray-400">ID: <?php echo e($tenant->id); ?></div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="text-right">
                                    <div class="text-sm text-gray-900">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            <?php if($tenant->subscription_status === 'active'): ?> bg-green-100 text-green-800
                                            <?php elseif($tenant->subscription_status === 'suspended'): ?> bg-red-100 text-red-800
                                            <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                            <?php echo e(ucfirst($tenant->subscription_status)); ?>

                                        </span>
                                    </div>
                                    <div class="text-xs text-gray-500"><?php echo e(ucfirst($tenant->subscription_plan)); ?></div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm text-gray-900">Domínios:</div>
                                    <?php $__currentLoopData = $tenant->domains; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $domain): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="text-xs text-gray-500"><?php echo e($domain->domain); ?></div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <div class="flex space-x-2">
                                    <button 
                                        @click="editTenant('<?php echo e($tenant->id); ?>')"
                                        class="text-blue-600 hover:text-blue-900 text-sm"
                                    >
                                        Editar
                                    </button>
                                    <button 
                                        @click="deleteTenant('<?php echo e($tenant->id); ?>')"
                                        class="text-red-600 hover:text-red-900 text-sm"
                                    >
                                        Excluir
                                    </button>
                                </div>
                            </div>
                        </div>
                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                <?php echo e($tenants->links()); ?>

            </div>
        </div>
    </div>

    <script>
        function tenantsManager() {
            return {
                searchTerm: '',
                showCreateModal: false,
                
                editTenant(id) {
                    // Implementar edição
                    alert('Editar tenant: ' + id);
                },
                
                deleteTenant(id) {
                    if (confirm('Tem certeza que deseja excluir este tenant?')) {
                        // Implementar exclusão
                        alert('Excluir tenant: ' + id);
                    }
                }
            }
        }
    </script>
</body>
</html>
<?php /**PATH D:\Projetos\locbem-new\api\resources\views/tenants/index.blade.php ENDPATH**/ ?>