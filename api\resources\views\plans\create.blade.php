<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LocBem - Novo Plano</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Novo Plano</h1>
                        <p class="mt-2 text-gray-600">Criar um novo plano de assinatura</p>
                    </div>
                    <div>
                        <a href="{{ route('plans.index') }}" 
                           class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            Voltar
                        </a>
                    </div>
                </div>
            </div>

            <!-- Form -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <form method="POST" action="{{ route('plans.store') }}">
                    @csrf
                    
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Informações do Plano</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500">Configure os detalhes do novo plano</p>
                    </div>
                    
                    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                        <div class="grid grid-cols-1 gap-6">
                            <!-- Nome -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700">Nome do Plano *</label>
                                <input type="text" name="name" id="name" required
                                       value="{{ old('name') }}"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>

                            <!-- Descrição -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700">Descrição</label>
                                <textarea name="description" id="description" rows="3"
                                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">{{ old('description') }}</textarea>
                            </div>

                            <!-- Preço -->
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700">Preço Mensal (R$)</label>
                                <input type="number" name="price" id="price" step="0.01" min="0"
                                       value="{{ old('price') }}"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>

                            <!-- Limites -->
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="limit_clientes" class="block text-sm font-medium text-gray-700">Limite de Clientes</label>
                                    <input type="number" name="limit_clientes" id="limit_clientes" min="-1"
                                           value="{{ old('limit_clientes') }}"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <p class="mt-1 text-sm text-gray-500">Use -1 para ilimitado</p>
                                </div>

                                <div>
                                    <label for="limit_equipamentos" class="block text-sm font-medium text-gray-700">Limite de Equipamentos</label>
                                    <input type="number" name="limit_equipamentos" id="limit_equipamentos" min="-1"
                                           value="{{ old('limit_equipamentos') }}"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <p class="mt-1 text-sm text-gray-500">Use -1 para ilimitado</p>
                                </div>

                                <div>
                                    <label for="limit_contratos" class="block text-sm font-medium text-gray-700">Limite de Contratos</label>
                                    <input type="number" name="limit_contratos" id="limit_contratos" min="-1"
                                           value="{{ old('limit_contratos') }}"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <p class="mt-1 text-sm text-gray-500">Use -1 para ilimitado</p>
                                </div>

                                <div>
                                    <label for="limit_usuarios" class="block text-sm font-medium text-gray-700">Limite de Usuários</label>
                                    <input type="number" name="limit_usuarios" id="limit_usuarios" min="-1"
                                           value="{{ old('limit_usuarios') }}"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <p class="mt-1 text-sm text-gray-500">Use -1 para ilimitado</p>
                                </div>
                            </div>

                            <!-- Recursos -->
                            <div>
                                <label for="features" class="block text-sm font-medium text-gray-700">Recursos (um por linha)</label>
                                <textarea name="features" id="features" rows="6"
                                          placeholder="Relatórios básicos&#10;Suporte por email&#10;Backup automático"
                                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">{{ old('features') }}</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                        <button type="submit"
                                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Criar Plano
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded shadow-lg">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded shadow-lg">
            {{ session('error') }}
        </div>
    @endif

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.fixed.top-4.right-4');
            alerts.forEach(alert => {
                alert.style.display = 'none';
            });
        }, 5000);
    </script>
</body>
</html>
