<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Stancl\Tenancy\Database\Models\Domain;
use Stancl\Tenancy\Exceptions\TenantCouldNotBeIdentifiedException;
use Symfony\Component\HttpFoundation\Response;

class InitializeTenancyByOrigin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            // Tentar identificar o tenant pelo Origin ou Referer
            $originDomain = $this->getOriginDomain($request);
            
            if (!$originDomain) {
                throw new TenantCouldNotBeIdentifiedException('No origin domain found in request headers');
            }

            // Buscar o tenant pelo domínio de origem
            $domain = Domain::where('domain', $originDomain)->first();
            
            if (!$domain) {
                throw new TenantCouldNotBeIdentifiedException("Tenant could not be identified on origin domain {$originDomain}");
            }

            // Inicializar o contexto do tenant
            $this->initializeTenancy($domain->tenant_id);

        } catch (TenantCouldNotBeIdentifiedException $e) {
            // Se não conseguir identificar o tenant, retornar erro
            return response()->json([
                'message' => $e->getMessage(),
                'error' => 'TENANT_NOT_IDENTIFIED',
                'origin_domain' => $this->getOriginDomain($request),
                'available_headers' => [
                    'origin' => $request->header('Origin'),
                    'referer' => $request->header('Referer'),
                    'host' => $request->header('Host'),
                ]
            ], 400);
        }

        return $next($request);
    }

    /**
     * Extrair o domínio de origem da requisição
     */
    private function getOriginDomain(Request $request): ?string
    {
        // Primeiro, tentar pelo header Origin
        $origin = $request->header('Origin');
        if ($origin) {
            $domain = $this->extractDomainFromUrl($origin);
            if ($domain) {
                return $domain;
            }
        }

        // Se não tiver Origin, tentar pelo Referer
        $referer = $request->header('Referer');
        if ($referer) {
            $domain = $this->extractDomainFromUrl($referer);
            if ($domain) {
                return $domain;
            }
        }

        // Como fallback, tentar alguns headers customizados que podem ser enviados pelo frontend
        $customOrigin = $request->header('X-Origin-Domain');
        if ($customOrigin) {
            return $customOrigin;
        }

        return null;
    }

    /**
     * Extrair domínio de uma URL
     */
    private function extractDomainFromUrl(string $url): ?string
    {
        $parsed = parse_url($url);
        
        if (!isset($parsed['host'])) {
            return null;
        }

        $domain = $parsed['host'];
        
        // Incluir a porta se existir
        if (isset($parsed['port'])) {
            $domain .= ':' . $parsed['port'];
        }

        return $domain;
    }

    /**
     * Inicializar o contexto do tenant
     */
    private function initializeTenancy(string $tenantId): void
    {
        // Buscar o tenant pelo ID
        $tenant = app(config('tenancy.tenant_model'))->find($tenantId);

        if (!$tenant) {
            throw new TenantCouldNotBeIdentifiedException("Tenant {$tenantId} not found");
        }

        // Inicializar o contexto do tenant
        tenancy()->initialize($tenant);
    }
}
