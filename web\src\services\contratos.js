import api from './api.js'

export const contratosService = {
  // Listar contratos com filtros e paginação
  async listar(params = {}) {
    try {
      const response = await api.get('/contratos', { params })
      return response.data
    } catch (error) {
      console.error('Erro ao listar contratos:', error)
      throw error
    }
  },

  // Buscar contrato por ID
  async buscarPorId(id) {
    try {
      const response = await api.get(`/contratos/${id}`)
      return response.data
    } catch (error) {
      console.error('Erro ao buscar contrato:', error)
      throw error
    }
  },

  // Criar novo contrato
  async criar(contrato) {
    try {
      const response = await api.post('/contratos', contrato)
      return response.data
    } catch (error) {
      console.error('Erro ao criar contrato:', error)
      throw error
    }
  },

  // Atualizar contrato
  async atualizar(id, contrato) {
    try {
      const response = await api.put(`/contratos/${id}`, contrato)
      return response.data
    } catch (error) {
      console.error('Erro ao atualizar contrato:', error)
      throw error
    }
  },

  // Excluir contrato
  async excluir(id) {
    try {
      const response = await api.delete(`/contratos/${id}`)
      return response.data
    } catch (error) {
      console.error('Erro ao excluir contrato:', error)
      throw error
    }
  },

  // Finalizar contrato
  async finalizar(id) {
    try {
      const response = await api.post(`/contratos/${id}/finalizar`)
      return response.data
    } catch (error) {
      console.error('Erro ao finalizar contrato:', error)
      throw error
    }
  }
}
