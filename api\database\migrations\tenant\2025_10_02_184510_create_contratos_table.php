<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contratos', function (Blueprint $table) {
            $table->id();
            $table->string('numero_contrato')->unique();
            $table->foreignId('cliente_id')->constrained('clientes');
            $table->foreignId('periodo_locacao_id')->nullable()->constrained('periodos_locacao');

            // Datas do contrato
            $table->date('data_inicio');
            $table->date('data_fim');
            $table->date('data_prevista_devolucao');
            $table->date('data_devolucao_real')->nullable();

            // Endereço de entrega/locação
            $table->string('endereco_entrega_cep', 10);
            $table->string('endereco_entrega_logradouro');
            $table->string('endereco_entrega_numero', 20);
            $table->string('endereco_entrega_complemento', 100)->nullable();
            $table->string('endereco_entrega_bairro', 100);
            $table->string('endereco_entrega_cidade', 100);
            $table->string('endereco_entrega_uf', 2);

            // Valores
            $table->decimal('valor_total', 12, 2)->default(0);
            $table->decimal('valor_desconto', 12, 2)->default(0);
            $table->decimal('valor_final', 12, 2)->default(0);

            // Informações adicionais
            $table->text('observacoes')->nullable();
            $table->text('condicoes_especiais')->nullable();

            // Status e controle
            $table->enum('status', ['pendente', 'ativo', 'em_retirada', 'finalizado', 'cancelado', 'renovado'])->default('pendente');
            $table->boolean('is_active')->default(true);

            // Campos para gestão de faturas
            $table->enum('forma_pagamento', ['dinheiro', 'pix', 'cartao_credito', 'cartao_debito', 'boleto', 'transferencia'])->nullable();
            $table->integer('parcelas')->default(1);
            $table->date('data_vencimento_primeira_parcela')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->index(['cliente_id', 'status']);
            $table->index(['data_inicio', 'data_prevista_devolucao']);
            $table->index('numero_contrato');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contratos');
    }
};
