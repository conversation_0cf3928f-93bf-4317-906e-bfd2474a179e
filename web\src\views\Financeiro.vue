<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-2xl font-semibold text-gray-900">Financeiro</h1>
        <p class="mt-2 text-sm text-gray-700">
          Gestão financeira completa - contas a receber, pagar e relatórios.
        </p>
      </div>
    </div>

    <!-- Dashboard Cards -->
    <div v-if="dashboardData" class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <!-- Contas a Receber Pendentes -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">A Receber</dt>
                <dd class="text-lg font-medium text-gray-900">
                  R$ {{ formatCurrency(dashboardData.resumo.contas_receber_pendentes) }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Contas a Pagar Pendentes -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">A Pagar</dt>
                <dd class="text-lg font-medium text-gray-900">
                  R$ {{ formatCurrency(dashboardData.resumo.contas_pagar_pendentes) }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Receitas do Mês -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Receitas do Mês</dt>
                <dd class="text-lg font-medium text-gray-900">
                  R$ {{ formatCurrency(dashboardData.resumo.receitas_mes) }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Saldo do Mês -->
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6" :class="dashboardData.resumo.saldo_mes >= 0 ? 'text-green-400' : 'text-red-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Saldo do Mês</dt>
                <dd class="text-lg font-medium" :class="dashboardData.resumo.saldo_mes >= 0 ? 'text-green-600' : 'text-red-600'">
                  R$ {{ formatCurrency(dashboardData.resumo.saldo_mes) }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="[
            activeTab === tab.id
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
            'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
          ]"
        >
          {{ tab.name }}
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="mt-6">
      <!-- Contas a Receber -->
      <div v-if="activeTab === 'receber'" class="space-y-6">
        <div class="sm:flex sm:items-center">
          <div class="sm:flex-auto">
            <h2 class="text-lg font-medium text-gray-900">Contas a Receber</h2>
          </div>
          <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-2">
            <button
              @click="gerarRelatorioContasReceber"
              type="button"
              class="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Relatório PDF
            </button>
            <button
              @click="novaContaReceber"
              type="button"
              class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Nova Conta
            </button>
          </div>
        </div>

        <!-- Filters for Contas a Receber -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Buscar</label>
                <input
                  v-model="filtrosReceber.buscar"
                  type="text"
                  placeholder="Descrição, cliente..."
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  @input="debouncedSearchReceber"
                />
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700">Status</label>
                <select
                  v-model="filtrosReceber.status"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  @change="loadContasReceber"
                >
                  <option value="">Todos</option>
                  <option value="pendente">Pendente</option>
                  <option value="pago">Pago</option>
                  <option value="vencido">Vencido</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">Data Início</label>
                <input
                  v-model="filtrosReceber.data_inicio"
                  type="date"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  @change="loadContasReceber"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700">Data Fim</label>
                <input
                  v-model="filtrosReceber.data_fim"
                  type="date"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  @change="loadContasReceber"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Table Contas a Receber -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <div v-if="loadingReceber" class="p-6 text-center">
            <div class="inline-flex items-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Carregando contas...
            </div>
          </div>
          
          <div v-else-if="contasReceber.data && contasReceber.data.length === 0" class="p-6 text-center text-gray-500">
            Nenhuma conta a receber encontrada.
          </div>
          
          <div v-else>
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cliente</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vencimento</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="relative px-6 py-3"><span class="sr-only">Ações</span></th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="conta in contasReceber.data" :key="conta.id">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {{ conta.descricao }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ conta.cliente?.nome_razao_social || 'N/A' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    R$ {{ formatCurrency(conta.valor) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(conta.data_vencimento) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          :class="getStatusClass(conta.status)">
                      {{ getStatusLabel(conta.status) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      v-if="conta.status === 'pendente' || conta.status === 'vencido'"
                      @click="marcarComoPaga(conta)"
                      class="text-green-600 hover:text-green-900 mr-3"
                      title="Marcar como paga"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Contas a Pagar -->
      <div v-if="activeTab === 'pagar'" class="space-y-6">
        <div class="sm:flex sm:items-center">
          <div class="sm:flex-auto">
            <h2 class="text-lg font-medium text-gray-900">Contas a Pagar</h2>
          </div>
          <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button
              @click="novaContaPagar"
              type="button"
              class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Nova Conta
            </button>
          </div>
        </div>

        <!-- Placeholder for Contas a Pagar content -->
        <div class="bg-white shadow rounded-lg p-6 text-center">
          <p class="text-gray-500">Interface de Contas a Pagar em desenvolvimento...</p>
        </div>
      </div>

      <!-- Recibos -->
      <div v-if="activeTab === 'recibos'" class="space-y-6">
        <h2 class="text-lg font-medium text-gray-900">Recibos</h2>
        
        <!-- Placeholder for Recibos content -->
        <div class="bg-white shadow rounded-lg p-6 text-center">
          <p class="text-gray-500">Interface de Recibos em desenvolvimento...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth.js'
import { financeiroService } from '../services/financeiro.js'
import { pdfService } from '../services/pdf.js'

export default {
  name: 'Financeiro',
  setup() {
    const authStore = useAuthStore()
    const activeTab = ref('receber')
    const dashboardData = ref(null)
    const contasReceber = ref({})
    const contasPagar = ref({})
    const recibos = ref({})
    const loadingReceber = ref(false)
    const loadingPagar = ref(false)
    const loadingRecibos = ref(false)

    const tabs = [
      { id: 'receber', name: 'Contas a Receber' },
      { id: 'pagar', name: 'Contas a Pagar' },
      { id: 'recibos', name: 'Recibos' }
    ]

    const filtrosReceber = ref({
      buscar: '',
      status: '',
      data_inicio: '',
      data_fim: ''
    })

    const hasPermission = (permission) => {
      return authStore.hasPermission(permission)
    }

    const formatDate = (date) => {
      if (!date) return ''
      return new Date(date).toLocaleDateString('pt-BR')
    }

    const formatCurrency = (value) => {
      if (!value) return '0,00'
      return new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value)
    }

    const getStatusClass = (status) => {
      const classes = {
        'pendente': 'bg-yellow-100 text-yellow-800',
        'pago': 'bg-green-100 text-green-800',
        'vencido': 'bg-red-100 text-red-800',
        'cancelado': 'bg-gray-100 text-gray-800'
      }
      return classes[status] || 'bg-gray-100 text-gray-800'
    }

    const getStatusLabel = (status) => {
      const labels = {
        'pendente': 'Pendente',
        'pago': 'Pago',
        'vencido': 'Vencido',
        'cancelado': 'Cancelado'
      }
      return labels[status] || status
    }

    const loadDashboard = async () => {
      try {
        const response = await financeiroService.dashboard()
        if (response.success) {
          dashboardData.value = response.data
        }
      } catch (error) {
        console.error('Erro ao carregar dashboard:', error)
      }
    }

    const loadContasReceber = async (page = 1) => {
      loadingReceber.value = true

      try {
        const params = {
          page,
          ...filtrosReceber.value
        }

        // Remove empty filters
        Object.keys(params).forEach(key => {
          if (params[key] === '') {
            delete params[key]
          }
        })

        const response = await financeiroService.contasReceber(params)

        if (response.success) {
          contasReceber.value = response.data
        }
      } catch (error) {
        console.error('Erro ao carregar contas a receber:', error)
      } finally {
        loadingReceber.value = false
      }
    }

    // Debounce search
    let searchTimeout
    const debouncedSearchReceber = () => {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        loadContasReceber()
      }, 500)
    }

    const novaContaReceber = () => {
      // TODO: Implementar modal de nova conta a receber
      alert('Funcionalidade em desenvolvimento')
    }

    const novaContaPagar = () => {
      // TODO: Implementar modal de nova conta a pagar
      alert('Funcionalidade em desenvolvimento')
    }

    const marcarComoPaga = async (conta) => {
      const formaPagamento = prompt('Forma de pagamento:')
      if (!formaPagamento) return

      try {
        const response = await financeiroService.pagarContaReceber(conta.id, {
          forma_pagamento: formaPagamento
        })

        if (response.success) {
          loadContasReceber()
          loadDashboard()
        }
      } catch (error) {
        alert('Erro ao registrar pagamento')
        console.error('Erro ao registrar pagamento:', error)
      }
    }

    const gerarRelatorioContasReceber = async () => {
      try {
        await pdfService.relatorioContasReceber(filtrosReceber.value)
      } catch (error) {
        alert('Erro ao gerar relatório')
        console.error('Erro ao gerar relatório:', error)
      }
    }

    onMounted(() => {
      loadDashboard()
      loadContasReceber()
    })

    return {
      activeTab,
      tabs,
      dashboardData,
      contasReceber,
      contasPagar,
      recibos,
      loadingReceber,
      loadingPagar,
      loadingRecibos,
      filtrosReceber,
      hasPermission,
      formatDate,
      formatCurrency,
      getStatusClass,
      getStatusLabel,
      loadContasReceber,
      debouncedSearchReceber,
      novaContaReceber,
      novaContaPagar,
      marcarComoPaga,
      gerarRelatorioContasReceber
    }
  }
}
</script>
