<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recibos', function (Blueprint $table) {
            $table->id();
            $table->string('numero_recibo')->unique();
            $table->enum('tipo', ['entrada', 'saida']); // entrada/receita ou saída/despesa
            $table->decimal('valor', 12, 2);
            $table->date('data_emissao');
            $table->string('pessoa_empresa'); // pagador ou recebedor
            $table->string('descricao');
            $table->foreignId('conta_receber_id')->nullable()->constrained('contas_receber');
            $table->foreignId('conta_pagar_id')->nullable()->constrained('contas_pagar');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['tipo', 'data_emissao']);
            $table->index('numero_recibo');
            $table->index('pessoa_empresa');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recibos');
    }
};
