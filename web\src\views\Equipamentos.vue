<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-2xl font-semibold text-gray-900">Equipamentos</h1>
        <p class="mt-2 text-sm text-gray-700">
          Lista de todos os equipamentos disponíveis para locação.
        </p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-2">
        <button
          @click="gerarRelatorioEquipamentos"
          type="button"
          class="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Relatório PDF
        </button>
        <button
          v-if="hasPermission('equipamentos.create')"
          @click="abrirModal()"
          type="button"
          class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Novo Equipamento
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <div>
            <label for="search" class="block text-sm font-medium text-gray-700">Buscar</label>
            <input
              id="search"
              v-model="filters.buscar"
              type="text"
              placeholder="Nome, código SKU..."
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @input="debouncedSearch"
            />
          </div>
          
          <div>
            <label for="categoria" class="block text-sm font-medium text-gray-700">Categoria</label>
            <select
              id="categoria"
              v-model="filters.categoria_id"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @change="loadEquipamentos"
            >
              <option value="">Todas</option>
              <option v-for="categoria in categorias" :key="categoria.id" :value="categoria.id">
                {{ categoria.nome }}
              </option>
            </select>
          </div>
          
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
            <select
              id="status"
              v-model="filters.status"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @change="loadEquipamentos"
            >
              <option value="">Todos</option>
              <option value="disponivel">Disponível</option>
              <option value="manutencao">Manutenção</option>
              <option value="inativo">Inativo</option>
            </select>
          </div>
          
          <div>
            <label for="disponivel" class="block text-sm font-medium text-gray-700">Disponibilidade</label>
            <select
              id="disponivel"
              v-model="filters.disponivel"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @change="loadEquipamentos"
            >
              <option value="">Todos</option>
              <option value="1">Apenas Disponíveis</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div v-if="loading" class="p-6 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-sm text-gray-500">Carregando equipamentos...</p>
      </div>
      
      <div v-else-if="error" class="p-6 text-center text-red-600">
        {{ error }}
      </div>
      
      <div v-else>
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Equipamento
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Categoria
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Código SKU
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quantidade
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Valor Diária
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="relative px-6 py-3">
                <span class="sr-only">Ações</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="equipamento in equipamentos.data" :key="equipamento.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ equipamento.nome }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ equipamento.descricao }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ equipamento.categoria?.nome }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                {{ equipamento.codigo_sku }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div class="flex flex-col">
                  <span class="text-green-600">{{ equipamento.quantidade_disponivel }} disponível</span>
                  <span class="text-blue-600">{{ equipamento.quantidade_locada }} locado</span>
                  <span class="text-yellow-600">{{ equipamento.quantidade_manutencao }} manutenção</span>
                  <span class="text-gray-500">{{ equipamento.quantidade_total }} total</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                R$ {{ formatCurrency(equipamento.valor_diaria) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                      :class="getStatusClass(equipamento.status)">
                  {{ getStatusLabel(equipamento.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    v-if="hasPermission('equipamentos.edit')"
                    @click="abrirModal(equipamento)"
                    class="text-blue-600 hover:text-blue-900 p-1"
                    title="Editar equipamento"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  </button>
                  <button
                    v-if="hasPermission('equipamentos.delete')"
                    @click="confirmarExclusao(equipamento)"
                    class="text-red-600 hover:text-red-900 p-1"
                    title="Excluir equipamento"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        
        <!-- Pagination -->
        <div v-if="equipamentos.total > 0" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              :disabled="!equipamentos.prev_page_url"
              @click="changePage(equipamentos.current_page - 1)"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>
            <button
              :disabled="!equipamentos.next_page_url"
              @click="changePage(equipamentos.current_page + 1)"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Próximo
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Mostrando
                <span class="font-medium">{{ equipamentos.from }}</span>
                a
                <span class="font-medium">{{ equipamentos.to }}</span>
                de
                <span class="font-medium">{{ equipamentos.total }}</span>
                resultados
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  :disabled="!equipamentos.prev_page_url"
                  @click="changePage(equipamentos.current_page - 1)"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Anterior
                </button>
                <button
                  :disabled="!equipamentos.next_page_url"
                  @click="changePage(equipamentos.current_page + 1)"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Próximo
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de Equipamento -->
    <EquipamentoModal
      :is-open="modalAberto"
      :equipamento="equipamentoSelecionado"
      @close="fecharModal"
      @saved="equipamentoSalvo"
    />

    <!-- Modal de Confirmação de Exclusão -->
    <div v-if="modalExclusao" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mt-4">Confirmar Exclusão</h3>
          <div class="mt-2 px-7 py-3">
            <p class="text-sm text-gray-500">
              Tem certeza que deseja excluir o equipamento <strong>{{ equipamentoParaExcluir?.nome }}</strong>?
              Esta ação não pode ser desfeita.
            </p>
          </div>
          <div class="items-center px-4 py-3">
            <button
              @click="cancelarExclusao"
              class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-gray-600"
            >
              Cancelar
            </button>
            <button
              @click="excluirEquipamento"
              :disabled="excluindo"
              class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 hover:bg-red-700 disabled:opacity-50"
            >
              {{ excluindo ? 'Excluindo...' : 'Excluir' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth.js'
import { equipamentosService } from '../services/equipamentos.js'
import { pdfService } from '../services/pdf.js'
import EquipamentoModal from '../components/EquipamentoModal.vue'

export default {
  name: 'Equipamentos',
  components: {
    EquipamentoModal
  },
  setup() {
    const authStore = useAuthStore()
    const equipamentos = ref({})
    const categorias = ref([])
    const loading = ref(false)
    const error = ref('')
    
    const filters = ref({
      buscar: '',
      categoria_id: '',
      status: '',
      disponivel: ''
    })

    // Modal states
    const modalAberto = ref(false)
    const equipamentoSelecionado = ref(null)
    const modalExclusao = ref(false)
    const equipamentoParaExcluir = ref(null)
    const excluindo = ref(false)
    
    const hasPermission = (permission) => {
      return authStore.hasPermission(permission)
    }
    
    const formatCurrency = (value) => {
      return new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value)
    }
    
    const getStatusClass = (status) => {
      const classes = {
        'disponivel': 'bg-green-100 text-green-800',
        'manutencao': 'bg-yellow-100 text-yellow-800',
        'inativo': 'bg-red-100 text-red-800'
      }
      return classes[status] || 'bg-gray-100 text-gray-800'
    }
    
    const getStatusLabel = (status) => {
      const labels = {
        'disponivel': 'Disponível',
        'manutencao': 'Manutenção',
        'inativo': 'Inativo'
      }
      return labels[status] || status
    }
    
    const loadCategorias = async () => {
      try {
        const response = await api.get('/categorias')
        if (response.data.success) {
          categorias.value = response.data.data
        }
      } catch (err) {
        console.error('Erro ao carregar categorias:', err)
      }
    }
    
    const loadEquipamentos = async (page = 1) => {
      loading.value = true
      error.value = ''
      
      try {
        const params = {
          page,
          ...filters.value
        }
        
        // Remove empty filters
        Object.keys(params).forEach(key => {
          if (params[key] === '') {
            delete params[key]
          }
        })
        
        const response = await equipamentosService.listar(params)

        if (response.success) {
          equipamentos.value = response.data
        } else {
          error.value = response.message
        }
      } catch (err) {
        error.value = 'Erro ao carregar equipamentos'
        console.error('Erro ao carregar equipamentos:', err)
      } finally {
        loading.value = false
      }
    }
    
    const changePage = (page) => {
      loadEquipamentos(page)
    }
    
    // Debounce search
    let searchTimeout
    const debouncedSearch = () => {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        loadEquipamentos()
      }, 500)
    }
    
    // Funções do modal
    const abrirModal = (equipamento = null) => {
      equipamentoSelecionado.value = equipamento
      modalAberto.value = true
    }

    const fecharModal = () => {
      modalAberto.value = false
      equipamentoSelecionado.value = null
    }

    const equipamentoSalvo = () => {
      loadEquipamentos()
    }

    const confirmarExclusao = (equipamento) => {
      equipamentoParaExcluir.value = equipamento
      modalExclusao.value = true
    }

    const cancelarExclusao = () => {
      modalExclusao.value = false
      equipamentoParaExcluir.value = null
    }

    const excluirEquipamento = async () => {
      if (!equipamentoParaExcluir.value) return

      try {
        excluindo.value = true
        const response = await equipamentosService.excluir(equipamentoParaExcluir.value.id)

        if (response.success) {
          loadEquipamentos()
          cancelarExclusao()
        } else {
          error.value = response.message
        }
      } catch (err) {
        error.value = 'Erro ao excluir equipamento'
        console.error('Erro ao excluir equipamento:', err)
      } finally {
        excluindo.value = false
      }
    }

    const gerarRelatorioEquipamentos = async () => {
      try {
        await pdfService.relatorioEquipamentos(filters.value)
      } catch (error) {
        alert('Erro ao gerar relatório')
        console.error('Erro ao gerar relatório:', error)
      }
    }

    onMounted(() => {
      loadCategorias()
      loadEquipamentos()
    })

    return {
      equipamentos,
      categorias,
      loading,
      error,
      filters,
      hasPermission,
      formatCurrency,
      getStatusClass,
      getStatusLabel,
      loadEquipamentos,
      changePage,
      debouncedSearch,
      // Modal
      modalAberto,
      equipamentoSelecionado,
      modalExclusao,
      equipamentoParaExcluir,
      excluindo,
      abrirModal,
      fecharModal,
      equipamentoSalvo,
      confirmarExclusao,
      cancelarExclusao,
      excluirEquipamento,
      gerarRelatorioEquipamentos
    }
  }
}
</script>
