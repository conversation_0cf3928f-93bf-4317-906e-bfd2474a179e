<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Cliente;
use App\Models\Equipamento;
use App\Models\Contrato;
use App\Models\ContaReceber;
use App\Models\ContaPagar;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class RelatorioController extends Controller
{
    /**
     * Dashboard executivo com métricas principais
     */
    public function dashboardExecutivo(): JsonResponse
    {
        try {
            $hoje = now();
            $inicioMes = $hoje->copy()->startOfMonth();
            $fimMes = $hoje->copy()->endOfMonth();
            $mesPassado = $hoje->copy()->subMonth();
            $inicioMesPassado = $mesPassado->copy()->startOfMonth();
            $fimMesPassado = $mesPassado->copy()->endOfMonth();

            // Métricas principais
            $metricas = [
                'receita_mes_atual' => ContaReceber::where('status', 'pago')
                    ->whereBetween('data_pagamento', [$inicioMes, $fimMes])
                    ->sum('valor'),
                
                'receita_mes_passado' => ContaReceber::where('status', 'pago')
                    ->whereBetween('data_pagamento', [$inicioMesPassado, $fimMesPassado])
                    ->sum('valor'),
                
                'contratos_ativos' => Contrato::where('status', 'ativo')->count(),
                
                'equipamentos_locados' => Equipamento::where('quantidade_locada', '>', 0)->count(),
                
                'clientes_ativos' => Cliente::whereHas('contratos', function($query) {
                    $query->where('status', 'ativo');
                })->count(),
                
                'contas_vencidas' => ContaReceber::where('status', 'vencido')->count(),
                
                'valor_contas_vencidas' => ContaReceber::where('status', 'vencido')->sum('valor'),
            ];

            // Crescimento percentual
            $crescimentoReceita = $metricas['receita_mes_passado'] > 0 
                ? (($metricas['receita_mes_atual'] - $metricas['receita_mes_passado']) / $metricas['receita_mes_passado']) * 100
                : 0;

            // Receita por mês (últimos 6 meses)
            $receitaPorMes = [];
            for ($i = 5; $i >= 0; $i--) {
                $mes = $hoje->copy()->subMonths($i);
                $inicioMes = $mes->copy()->startOfMonth();
                $fimMes = $mes->copy()->endOfMonth();
                
                $receita = ContaReceber::where('status', 'pago')
                    ->whereBetween('data_pagamento', [$inicioMes, $fimMes])
                    ->sum('valor');
                
                $receitaPorMes[] = [
                    'mes' => $mes->format('M/Y'),
                    'valor' => $receita
                ];
            }

            // Top 5 clientes por receita
            $topClientes = Cliente::select('clientes.*')
                ->selectRaw('SUM(contas_receber.valor) as total_receita')
                ->join('contas_receber', 'clientes.id', '=', 'contas_receber.cliente_id')
                ->where('contas_receber.status', 'pago')
                ->groupBy('clientes.id')
                ->orderBy('total_receita', 'desc')
                ->limit(5)
                ->get();

            // Equipamentos mais locados
            $equipamentosMaisLocados = Equipamento::select('equipamentos.*', 'categorias.nome as categoria_nome')
                ->selectRaw('COUNT(contrato_itens.id) as total_locacoes')
                ->join('categorias', 'equipamentos.categoria_id', '=', 'categorias.id')
                ->leftJoin('contrato_itens', 'equipamentos.id', '=', 'contrato_itens.equipamento_id')
                ->groupBy('equipamentos.id')
                ->orderBy('total_locacoes', 'desc')
                ->limit(5)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'metricas' => $metricas,
                    'crescimento_receita' => round($crescimentoReceita, 2),
                    'receita_por_mes' => $receitaPorMes,
                    'top_clientes' => $topClientes,
                    'equipamentos_mais_locados' => $equipamentosMaisLocados
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao gerar dashboard executivo',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Análise de performance de equipamentos
     */
    public function performanceEquipamentos(Request $request): JsonResponse
    {
        try {
            $dataInicio = $request->get('data_inicio', now()->subMonths(3)->format('Y-m-d'));
            $dataFim = $request->get('data_fim', now()->format('Y-m-d'));

            // Taxa de utilização por equipamento
            $utilizacaoEquipamentos = Equipamento::select('equipamentos.*', 'categorias.nome as categoria_nome')
                ->selectRaw('
                    COUNT(contrato_itens.id) as total_locacoes,
                    SUM(contrato_itens.dias_locacao * contrato_itens.quantidade) as total_dias_locados,
                    AVG(contrato_itens.valor_unitario) as valor_medio_diario
                ')
                ->join('categorias', 'equipamentos.categoria_id', '=', 'categorias.id')
                ->leftJoin('contrato_itens', 'equipamentos.id', '=', 'contrato_itens.equipamento_id')
                ->leftJoin('contratos', 'contrato_itens.contrato_id', '=', 'contratos.id')
                ->whereBetween('contratos.data_inicio', [$dataInicio, $dataFim])
                ->groupBy('equipamentos.id')
                ->orderBy('total_dias_locados', 'desc')
                ->get();

            // Receita por categoria
            $receitaPorCategoria = DB::table('categorias')
                ->select('categorias.nome', DB::raw('SUM(contrato_itens.valor_total) as receita_total'))
                ->join('equipamentos', 'categorias.id', '=', 'equipamentos.categoria_id')
                ->join('contrato_itens', 'equipamentos.id', '=', 'contrato_itens.equipamento_id')
                ->join('contratos', 'contrato_itens.contrato_id', '=', 'contratos.id')
                ->whereBetween('contratos.data_inicio', [$dataInicio, $dataFim])
                ->groupBy('categorias.id', 'categorias.nome')
                ->orderBy('receita_total', 'desc')
                ->get();

            // Equipamentos ociosos (não locados no período)
            $equipamentosOciosos = Equipamento::select('equipamentos.*', 'categorias.nome as categoria_nome')
                ->join('categorias', 'equipamentos.categoria_id', '=', 'categorias.id')
                ->whereNotExists(function($query) use ($dataInicio, $dataFim) {
                    $query->select(DB::raw(1))
                        ->from('contrato_itens')
                        ->join('contratos', 'contrato_itens.contrato_id', '=', 'contratos.id')
                        ->whereColumn('contrato_itens.equipamento_id', 'equipamentos.id')
                        ->whereBetween('contratos.data_inicio', [$dataInicio, $dataFim]);
                })
                ->where('equipamentos.status', 'disponivel')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'utilizacao_equipamentos' => $utilizacaoEquipamentos,
                    'receita_por_categoria' => $receitaPorCategoria,
                    'equipamentos_ociosos' => $equipamentosOciosos,
                    'periodo' => [
                        'inicio' => $dataInicio,
                        'fim' => $dataFim
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao gerar relatório de performance',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Análise financeira detalhada
     */
    public function analiseFinanceira(Request $request): JsonResponse
    {
        try {
            $dataInicio = $request->get('data_inicio', now()->subMonths(6)->format('Y-m-d'));
            $dataFim = $request->get('data_fim', now()->format('Y-m-d'));

            // Fluxo de caixa mensal
            $fluxoCaixa = [];
            $inicio = Carbon::parse($dataInicio)->startOfMonth();
            $fim = Carbon::parse($dataFim)->endOfMonth();
            
            while ($inicio <= $fim) {
                $inicioMes = $inicio->copy()->startOfMonth();
                $fimMes = $inicio->copy()->endOfMonth();
                
                $receitas = ContaReceber::where('status', 'pago')
                    ->whereBetween('data_pagamento', [$inicioMes, $fimMes])
                    ->sum('valor');
                
                $despesas = ContaPagar::where('status', 'pago')
                    ->whereBetween('data_pagamento', [$inicioMes, $fimMes])
                    ->sum('valor');
                
                $fluxoCaixa[] = [
                    'mes' => $inicio->format('M/Y'),
                    'receitas' => $receitas,
                    'despesas' => $despesas,
                    'saldo' => $receitas - $despesas
                ];
                
                $inicio->addMonth();
            }

            // Análise de inadimplência
            $inadimplencia = [
                'total_vencidas' => ContaReceber::where('status', 'vencido')->count(),
                'valor_vencidas' => ContaReceber::where('status', 'vencido')->sum('valor'),
                'media_dias_atraso' => ContaReceber::where('status', 'vencido')
                    ->selectRaw('AVG(DATEDIFF(NOW(), data_vencimento)) as media_dias')
                    ->value('media_dias') ?? 0,
                'clientes_inadimplentes' => ContaReceber::where('status', 'vencido')
                    ->distinct('cliente_id')
                    ->count('cliente_id')
            ];

            // Despesas por categoria
            $despesasPorCategoria = ContaPagar::select('categoria', DB::raw('SUM(valor) as total'))
                ->where('status', 'pago')
                ->whereBetween('data_pagamento', [$dataInicio, $dataFim])
                ->groupBy('categoria')
                ->orderBy('total', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'fluxo_caixa' => $fluxoCaixa,
                    'inadimplencia' => $inadimplencia,
                    'despesas_por_categoria' => $despesasPorCategoria,
                    'periodo' => [
                        'inicio' => $dataInicio,
                        'fim' => $dataFim
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao gerar análise financeira',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Relatório de clientes
     */
    public function relatorioClientes(Request $request): JsonResponse
    {
        try {
            // Clientes mais ativos
            $clientesMaisAtivos = Cliente::select('clientes.*')
                ->selectRaw('COUNT(contratos.id) as total_contratos')
                ->selectRaw('SUM(contratos.valor_final) as valor_total_contratos')
                ->leftJoin('contratos', 'clientes.id', '=', 'contratos.cliente_id')
                ->groupBy('clientes.id')
                ->orderBy('total_contratos', 'desc')
                ->limit(10)
                ->get();

            // Novos clientes por mês (últimos 6 meses)
            $novosClientesPorMes = [];
            for ($i = 5; $i >= 0; $i--) {
                $mes = now()->subMonths($i);
                $inicioMes = $mes->copy()->startOfMonth();
                $fimMes = $mes->copy()->endOfMonth();
                
                $novosClientes = Cliente::whereBetween('created_at', [$inicioMes, $fimMes])->count();
                
                $novosClientesPorMes[] = [
                    'mes' => $mes->format('M/Y'),
                    'novos_clientes' => $novosClientes
                ];
            }

            // Análise de retenção
            $retencao = [
                'clientes_recorrentes' => Cliente::whereHas('contratos', function($query) {
                    $query->havingRaw('COUNT(*) > 1');
                })->count(),
                'taxa_retencao' => 0
            ];

            $totalClientes = Cliente::count();
            if ($totalClientes > 0) {
                $retencao['taxa_retencao'] = ($retencao['clientes_recorrentes'] / $totalClientes) * 100;
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'clientes_mais_ativos' => $clientesMaisAtivos,
                    'novos_clientes_por_mes' => $novosClientesPorMes,
                    'retencao' => $retencao
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erro ao gerar relatório de clientes',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
