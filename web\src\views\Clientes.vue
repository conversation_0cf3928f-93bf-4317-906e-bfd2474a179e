<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h1 class="text-2xl font-semibold text-gray-900">Clientes</h1>
        <p class="mt-2 text-sm text-gray-700">
          Lista de todos os clientes cadastrados no sistema.
        </p>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <button
          v-if="hasPermission('clientes.create')"
          @click="abrirModal()"
          type="button"
          class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          Novo Cliente
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div>
            <label for="search" class="block text-sm font-medium text-gray-700">Buscar</label>
            <input
              id="search"
              v-model="filters.buscar"
              type="text"
              placeholder="Nome, email, CPF/CNPJ..."
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @input="debouncedSearch"
            />
          </div>
          
          <div>
            <label for="tipo" class="block text-sm font-medium text-gray-700">Tipo</label>
            <select
              id="tipo"
              v-model="filters.tipo_pessoa"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @change="loadClientes"
            >
              <option value="">Todos</option>
              <option value="PF">Pessoa Física</option>
              <option value="PJ">Pessoa Jurídica</option>
            </select>
          </div>
          
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
            <select
              id="status"
              v-model="filters.ativo"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              @change="loadClientes"
            >
              <option value="">Todos</option>
              <option value="1">Ativo</option>
              <option value="0">Inativo</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div v-if="loading" class="p-6 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-sm text-gray-500">Carregando clientes...</p>
      </div>
      
      <div v-else-if="error" class="p-6 text-center text-red-600">
        {{ error }}
      </div>
      
      <div v-else>
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Cliente
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Tipo
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Documento
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contato
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="relative px-6 py-3">
                <span class="sr-only">Ações</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="cliente in clientes.data" :key="cliente.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ cliente.nome_razao_social }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ cliente.email }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                      :class="cliente.tipo_pessoa === 'PF' ? 'bg-blue-100 text-blue-800' : 'bg-indigo-100 text-indigo-800'">
                  {{ cliente.tipo_pessoa === 'PF' ? 'Pessoa Física' : 'Pessoa Jurídica' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ cliente.cpf_cnpj }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div>{{ cliente.telefone }}</div>
                <div class="text-gray-500">{{ cliente.celular }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                      :class="cliente.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                  {{ cliente.is_active ? 'Ativo' : 'Inativo' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    v-if="hasPermission('clientes.edit')"
                    @click="abrirModal(cliente)"
                    class="text-blue-600 hover:text-blue-900 p-1"
                    title="Editar cliente"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  </button>
                  <button
                    v-if="hasPermission('clientes.delete')"
                    @click="confirmarExclusao(cliente)"
                    class="text-red-600 hover:text-red-900 p-1"
                    title="Excluir cliente"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        
        <!-- Pagination -->
        <div v-if="clientes.total > 0" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              :disabled="!clientes.prev_page_url"
              @click="changePage(clientes.current_page - 1)"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Anterior
            </button>
            <button
              :disabled="!clientes.next_page_url"
              @click="changePage(clientes.current_page + 1)"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Próximo
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Mostrando
                <span class="font-medium">{{ clientes.from }}</span>
                a
                <span class="font-medium">{{ clientes.to }}</span>
                de
                <span class="font-medium">{{ clientes.total }}</span>
                resultados
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  :disabled="!clientes.prev_page_url"
                  @click="changePage(clientes.current_page - 1)"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Anterior
                </button>
                <button
                  :disabled="!clientes.next_page_url"
                  @click="changePage(clientes.current_page + 1)"
                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Próximo
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de Cliente -->
    <ClienteModal
      :is-open="modalAberto"
      :cliente="clienteSelecionado"
      @close="fecharModal"
      @saved="clienteSalvo"
    />

    <!-- Modal de Confirmação de Exclusão -->
    <div v-if="modalExclusao" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mt-4">Confirmar Exclusão</h3>
          <div class="mt-2 px-7 py-3">
            <p class="text-sm text-gray-500">
              Tem certeza que deseja excluir o cliente <strong>{{ clienteParaExcluir?.nome_razao_social }}</strong>?
              Esta ação não pode ser desfeita.
            </p>
          </div>
          <div class="items-center px-4 py-3">
            <button
              @click="cancelarExclusao"
              class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-gray-600"
            >
              Cancelar
            </button>
            <button
              @click="excluirCliente"
              :disabled="excluindo"
              class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 hover:bg-red-700 disabled:opacity-50"
            >
              {{ excluindo ? 'Excluindo...' : 'Excluir' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '../stores/auth.js'
import { clientesService } from '../services/clientes.js'
import ClienteModal from '../components/ClienteModal.vue'

export default {
  name: 'Clientes',
  components: {
    ClienteModal
  },
  setup() {
    const authStore = useAuthStore()
    const clientes = ref({})
    const loading = ref(false)
    const error = ref('')

    const filters = ref({
      buscar: '',
      tipo_pessoa: '',
      ativo: ''
    })

    // Modal states
    const modalAberto = ref(false)
    const clienteSelecionado = ref(null)
    const modalExclusao = ref(false)
    const clienteParaExcluir = ref(null)
    const excluindo = ref(false)
    
    const hasPermission = (permission) => {
      return authStore.hasPermission(permission)
    }
    
    const loadClientes = async (page = 1) => {
      loading.value = true
      error.value = ''

      try {
        const params = {
          page,
          ...filters.value
        }

        // Remove empty filters
        Object.keys(params).forEach(key => {
          if (params[key] === '') {
            delete params[key]
          }
        })

        const response = await clientesService.listar(params)

        if (response.success) {
          clientes.value = response.data
        } else {
          error.value = response.message
        }
      } catch (err) {
        error.value = 'Erro ao carregar clientes'
        console.error('Erro ao carregar clientes:', err)
      } finally {
        loading.value = false
      }
    }
    
    const changePage = (page) => {
      loadClientes(page)
    }
    
    // Debounce search
    let searchTimeout
    const debouncedSearch = () => {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        loadClientes()
      }, 500)
    }
    
    // Funções do modal
    const abrirModal = (cliente = null) => {
      clienteSelecionado.value = cliente
      modalAberto.value = true
    }

    const fecharModal = () => {
      modalAberto.value = false
      clienteSelecionado.value = null
    }

    const clienteSalvo = () => {
      loadClientes()
    }

    const confirmarExclusao = (cliente) => {
      clienteParaExcluir.value = cliente
      modalExclusao.value = true
    }

    const cancelarExclusao = () => {
      modalExclusao.value = false
      clienteParaExcluir.value = null
    }

    const excluirCliente = async () => {
      if (!clienteParaExcluir.value) return

      try {
        excluindo.value = true
        const response = await clientesService.excluir(clienteParaExcluir.value.id)

        if (response.success) {
          loadClientes()
          cancelarExclusao()
        } else {
          error.value = response.message
        }
      } catch (err) {
        error.value = 'Erro ao excluir cliente'
        console.error('Erro ao excluir cliente:', err)
      } finally {
        excluindo.value = false
      }
    }

    onMounted(() => {
      loadClientes()
    })

    return {
      clientes,
      loading,
      error,
      filters,
      hasPermission,
      loadClientes,
      changePage,
      debouncedSearch,
      // Modal
      modalAberto,
      clienteSelecionado,
      modalExclusao,
      clienteParaExcluir,
      excluindo,
      abrirModal,
      fecharModal,
      clienteSalvo,
      confirmarExclusao,
      cancelarExclusao,
      excluirCliente
    }
  }
}
</script>
