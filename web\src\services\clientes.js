import api from './api.js'

export const clientesService = {
  // Listar clientes com filtros e paginação
  async listar(params = {}) {
    try {
      const response = await api.get('/clientes', { params })
      return response.data
    } catch (error) {
      console.error('Erro ao listar clientes:', error)
      throw error
    }
  },

  // Buscar cliente por ID
  async buscarPorId(id) {
    try {
      const response = await api.get(`/clientes/${id}`)
      return response.data
    } catch (error) {
      console.error('Erro ao buscar cliente:', error)
      throw error
    }
  },

  // Criar novo cliente
  async criar(cliente) {
    try {
      const response = await api.post('/clientes', cliente)
      return response.data
    } catch (error) {
      console.error('Erro ao criar cliente:', error)
      throw error
    }
  },

  // Atualizar cliente
  async atualizar(id, cliente) {
    try {
      const response = await api.put(`/clientes/${id}`, cliente)
      return response.data
    } catch (error) {
      console.error('Erro ao atualizar cliente:', error)
      throw error
    }
  },

  // Excluir cliente
  async excluir(id) {
    try {
      const response = await api.delete(`/clientes/${id}`)
      return response.data
    } catch (error) {
      console.error('Erro ao excluir cliente:', error)
      throw error
    }
  }
}
