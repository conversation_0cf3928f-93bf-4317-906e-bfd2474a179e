<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contrato_itens', function (Blueprint $table) {
            $table->id();
            $table->foreignId('contrato_id')->constrained('contratos')->onDelete('cascade');
            $table->foreignId('equipamento_id')->constrained('equipamentos');
            $table->integer('quantidade');
            $table->decimal('valor_unitario_diaria', 10, 2);
            $table->decimal('valor_total_item', 12, 2);
            $table->date('data_retirada')->nullable();
            $table->date('data_devolucao')->nullable();
            $table->enum('status', ['pendente', 'retirado', 'devolvido'])->default('pendente');
            $table->text('observacoes')->nullable();
            $table->timestamps();

            $table->index(['contrato_id', 'status']);
            $table->index(['equipamento_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contrato_itens');
    }
};
