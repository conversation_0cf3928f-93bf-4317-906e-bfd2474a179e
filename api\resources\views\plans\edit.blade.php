<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LocBem - Editar Plano</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Editar Plano: {{ ucfirst($id) }}</h1>
                        <p class="mt-2 text-gray-600">Modificar configurações do plano</p>
                    </div>
                    <div>
                        <a href="{{ route('plans.index') }}" 
                           class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            Voltar
                        </a>
                    </div>
                </div>
            </div>

            <!-- Notice -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">
                            Funcionalidade em Desenvolvimento
                        </h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>A edição de planos está em desenvolvimento. Por enquanto, os planos são configurados estaticamente no código.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form -->
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <form method="POST" action="{{ route('plans.update', $id) }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="px-4 py-5 sm:px-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Informações do Plano</h3>
                        <p class="mt-1 max-w-2xl text-sm text-gray-500">Edite os detalhes do plano {{ ucfirst($id) }}</p>
                    </div>
                    
                    <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                        <div class="grid grid-cols-1 gap-6">
                            <!-- Nome -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700">Nome do Plano *</label>
                                <input type="text" name="name" id="name" required
                                       value="{{ ucfirst($id) }}"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>

                            <!-- Descrição -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700">Descrição</label>
                                <textarea name="description" id="description" rows="3"
                                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">Plano {{ ucfirst($id) }} para empresas</textarea>
                            </div>

                            <!-- Preço -->
                            <div>
                                <label for="price" class="block text-sm font-medium text-gray-700">Preço Mensal (R$)</label>
                                <input type="number" name="price" id="price" step="0.01" min="0"
                                       value="{{ $id === 'trial' ? '0' : ($id === 'basic' ? '49.90' : ($id === 'premium' ? '99.90' : '199.90')) }}"
                                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>

                            <!-- Status -->
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                                <select name="status" id="status"
                                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <option value="active">Ativo</option>
                                    <option value="inactive">Inativo</option>
                                </select>
                            </div>

                            <!-- Limites -->
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="limit_clientes" class="block text-sm font-medium text-gray-700">Limite de Clientes</label>
                                    <input type="number" name="limit_clientes" id="limit_clientes" min="-1"
                                           value="{{ $id === 'trial' ? '10' : ($id === 'basic' ? '50' : ($id === 'premium' ? '200' : '-1')) }}"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <p class="mt-1 text-sm text-gray-500">Use -1 para ilimitado</p>
                                </div>

                                <div>
                                    <label for="limit_equipamentos" class="block text-sm font-medium text-gray-700">Limite de Equipamentos</label>
                                    <input type="number" name="limit_equipamentos" id="limit_equipamentos" min="-1"
                                           value="{{ $id === 'trial' ? '20' : ($id === 'basic' ? '100' : ($id === 'premium' ? '500' : '-1')) }}"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <p class="mt-1 text-sm text-gray-500">Use -1 para ilimitado</p>
                                </div>

                                <div>
                                    <label for="limit_contratos" class="block text-sm font-medium text-gray-700">Limite de Contratos</label>
                                    <input type="number" name="limit_contratos" id="limit_contratos" min="-1"
                                           value="{{ $id === 'trial' ? '5' : ($id === 'basic' ? '25' : ($id === 'premium' ? '100' : '-1')) }}"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <p class="mt-1 text-sm text-gray-500">Use -1 para ilimitado</p>
                                </div>

                                <div>
                                    <label for="limit_usuarios" class="block text-sm font-medium text-gray-700">Limite de Usuários</label>
                                    <input type="number" name="limit_usuarios" id="limit_usuarios" min="-1"
                                           value="{{ $id === 'trial' ? '2' : ($id === 'basic' ? '5' : ($id === 'premium' ? '15' : '-1')) }}"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <p class="mt-1 text-sm text-gray-500">Use -1 para ilimitado</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                        <button type="submit"
                                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Salvar Alterações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded shadow-lg">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded shadow-lg">
            {{ session('error') }}
        </div>
    @endif

    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.fixed.top-4.right-4');
            alerts.forEach(alert => {
                alert.style.display = 'none';
            });
        }, 5000);
    </script>
</body>
</html>
